import React, { useContext, useEffect, useState } from 'react';
import './workArea.scss';
import { CircularProgress } from '@mui/material';
import axios from 'axios';
import { Base64 } from 'js-base64';
import CustomPagination from './components/CustomPagination';
import CustomTab from './components/CustomTab';
import CustomTable from './components/CustomTable';
import SearchBar from './components/SearchBar';
import { workAreaTabs, workAreaTableHeaders } from './constants';
import { Api } from '../../api/Api';
import { StoreContext } from '../../store/StoreProvider';

const WorkArea = () => {
    const [state, _] = useContext(StoreContext);
    const [tabId, setTabId] = useState('tempSave');
    const [reviewData, setReviewData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formattedReviewData, setFormattedReviewData] = useState([]);
    const [curPage, setCurPage] = useState(1);
    const [paginatedReviewData, setPaginatedReviewData] = useState([]);
    const [searchQuery, setSearchQuery] = useState('');
    const { chatgptDataset } = state.chatgpt;
    const { email } = state.user;
    const itemsPerPage = 10;

    const formatTime = time => {
        const date = new Date(parseInt(time, 10));

        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };

    const formatDataset = dataset => {
        const foundDataset = chatgptDataset.find(el => el.id === dataset);
        return foundDataset ? foundDataset.label : 'Unknown Dataset';
    };

    const formatData = data => {
        const newData = data
            .sort((a, b) => b.nerTime - a.nerTime)
            .map(el => ({
                ...el,
                nerTime: formatTime(el.nerTime),
                nerDataset: formatDataset(el.nerDataset)
            }));

        return newData;
    };

    const loadReviewData = async () => {
        const baseEncodeMail = Base64.encode(encodeURI(email));
        const url = Api.getUserReviewData(tabId, baseEncodeMail);

        try {
            setCurPage(1);
            setIsLoading(true);
            setReviewData([]);

            const res = await axios.get(url);

            setReviewData(res.data.data);
            setIsLoading(false);
        } catch (e) {
            console.log('load work area data error：', e);
            setIsLoading(false);
        }
    };

    useEffect(() => {
        if (!tabId || chatgptDataset.length === 0 || !email) {
            setReviewData([]);
            return;
        }

        loadReviewData();
    }, [tabId, chatgptDataset, email]);

    useEffect(() => {
        setFormattedReviewData(formatData(reviewData));
    }, [reviewData]);

    useEffect(() => {
        const filteredData = formattedReviewData.filter(item =>
            Object.values(item).some(value => value.toString().toLowerCase().includes(searchQuery.toLowerCase()))
        );

        const indexOfLastItem = curPage * itemsPerPage;
        const indexOfFirstItem = indexOfLastItem - itemsPerPage;
        const currentData = filteredData.slice(indexOfFirstItem, indexOfLastItem);

        setPaginatedReviewData(currentData);
    }, [formattedReviewData, curPage, searchQuery]);

    return (
        <div className="workAreaContainer">
            <h2 className="workAreaTitle">我的工作區</h2>
            <div className="workAreaPanel">
                <CustomTab tabs={workAreaTabs} tabId={tabId} setTabId={setTabId} />
                <SearchBar onSearch={query => setSearchQuery(query)} />
            </div>
            {isLoading ? (
                <CircularProgress sx={{ display: 'flex', margin: 'auto' }} />
            ) : (
                <CustomTable
                    headers={workAreaTableHeaders}
                    data={paginatedReviewData}
                    loadReviewData={loadReviewData}
                />
            )}
            {reviewData.length > 0 && (
                <CustomPagination
                    totalPages={Math.ceil(reviewData.length / itemsPerPage)}
                    curPage={curPage}
                    setCurPage={setCurPage}
                />
            )}
        </div>
    );
};

export default WorkArea;
