.signInPage {
    p,
    h1 {
        margin: 0;
    }
    height: 100vh;
    width: 100%;
    padding: 16px;
    background: linear-gradient(180deg, #f9fafc 0%, #f4f8fb 100%);
    .signInPageButton {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        border-radius: 8px;
        padding: 12px 16px;
        font-weight: 600;
        font-size: 14px;
        line-height: 20.12px;
        cursor: pointer;
        width: 100%;
    }
    .signInPageContainer {
        height: 100%;
        width: 100%;
        display: flex;
        justify-content: space-between;
        gap: 16px;
        .signInPageImg {
            height: 100%;
            width: 100%;
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
        .signInPageContent {
            height: 100%;
            width: 100%;
            padding: 80px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 40px;
            .signInPageTitle {
                width: 100%;
                font-weight: 600;
                font-size: 24px;
                line-height: 34.49px;
            }
            .signInPageButton--google {
                background-color: #ffffff;
                border: 1px solid #eeeeee;
                color: #336f89;
            }
            .signInPageForm {
                width: 100%;
                display: flex;
                flex-direction: column;
                gap: 16px;
                .signInPageField {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    margin-bottom: 1rem;
                    .signInPageLabel {
                        font-weight: 600;
                        font-size: 14px;
                        line-height: 20.12px;
                        .signInPageRequired {
                            color: red;
                        }
                    }
                    .signInPageInput {
                        flex: 1;
                        border-radius: 8px;
                        border: 1px solid #eeeeee;
                        padding: 8px 12px 8px 16px;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 20.12px;
                    }
                    .passwordInputContainer {
                        position: relative;
                        display: flex;
                        align-items: center;
                    }
                    .togglePasswordButton {
                        position: absolute;
                        right: 12px;
                        background: none;
                        border: none;
                        cursor: pointer;
                        font-size: 1rem;
                    }
                }
                .forgotPasswordContainer {
                    display: flex;
                    justify-content: end;
                    margin-bottom: 1rem;
                }
                .signInPageForgotPassword {
                    font-size: 14px;
                    line-height: 20.12px;
                    color: #336f89;
                    background-color: transparent;
                    border: none;
                    border-bottom: 1px solid black;
                    cursor: pointer;
                    &-disabled{
                        color: #BDBDBD;
                        cursor: not-allowed;
                    }
                }
                .signInPageButton--signIn {
                    border: 1px solid #336f89;
                    background-color: #336f89;
                    color: white;
                    margin-bottom: 1rem;
                }
                .signInPageSeparator {
                    text-align: center;
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 20.12px;
                    color: #9e9e9e;
                    margin-bottom: 1rem;
                }
                .signInPageButton--register {
                    background-color: #ffffff;
                    border: 1px solid #EEEEEE;
                    color: #336f89;
                }
                .firebaseui-card-content{
                    padding: 0;
                }
                .firebaseui-idp-button, .firebaseui-tenant-button{
                    max-width: initial;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    border:1px solid #EEEEEE;
                    border-radius: 8px;
                    box-shadow: initial;
                }
                .firebaseui-container{
                    display: flex;
                    flex-direction: column;
                    width: 100%;
                    margin: 0;
                    max-width: initial;
                }
                .firebaseui-idp-google>.firebaseui-idp-text{
                    color: #336F89;
                }
            }
        }
    }
}

::-ms-reveal {
    display: none;
}