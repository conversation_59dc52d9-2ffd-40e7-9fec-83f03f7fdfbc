import React, { useContext } from 'react';
import { Box } from '@mui/material';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormGroup from '@mui/material/FormGroup';
import Switch from '@mui/material/Switch';
import Typography from '@mui/material/Typography';
import Grid from '@mui/system/Unstable_Grid';
import TextOutput from './TextOutput';
import StatusIcon from '../../../pages/WorkArea/components/StatusIcon';
import { statusIconMap } from '../../../pages/WorkArea/constants';
import { StoreContext } from '../../../store/StoreProvider';

export default function ChatgptRight({ setChecked, checked, outputData, setOutputData }) {
    const [state] = useContext(StoreContext);
    const { serialNumber, status } = state.chatgpt;

    const statusIconMapping = type => {
        const statusDetail = statusIconMap.find(el => el.id === type);

        return (
            <StatusIcon
                text={statusDetail.text}
                bgColor={statusDetail.bgcolor}
                textColor={statusDetail.textColor}
            />
        );
    };

    return (
        <Grid xs className="chatgpt_right">
            <Grid
                className="chatgptHeader-right"
                sx={{ minHeight: '48px', display: 'flex', width: '100%' }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        width: '100%',
                        alignItems: 'center',
                        fontSize: '14px',
                        fontWeight: '600'
                    }}
                >
                    <Box sx={{ flexBasis: '50%' }}>
                        <p>
                            <span>編號：</span>
                            <span>{serialNumber}</span>
                        </p>
                    </Box>
                    <Box sx={{ flexBasis: '50%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <p>
                            <span>審核狀態：</span>
                            <span>{status && statusIconMapping(status)}</span>
                        </p>
                        </Box>

                    </Box>
                </Box>
            </Grid>
            <Grid
                sx={{
                    backgroundColor: '#ffffff',
                    padding: '24px 24px 24px 24px',
                    borderRadius: '12px',
                    marginTop: '0.5rem'
                    // height: '640px'
                }}
            >
                <Grid className="chatgpt_output" container>
                    <Typography variant="subtitle" sx={{ fontWeight: '600' }}>
                        GPT擷取結果
                    </Typography>
                    <Grid>
                        <FormGroup>
                            <FormControlLabel
                                className="control"
                                sx={{
                                    '&.control': {
                                        columnGap: '4px'
                                    },
                                    '& .MuiFormControlLabel-label': {
                                        fontWeight: '600'
                                    }
                                }}
                                control={
                                    <Switch
                                        onChange={() => {
                                            setChecked(!checked);
                                        }}
                                        sx={{
                                            '& .Mui-checked': {
                                                // color: '#00e68a'
                                            },
                                            '& .MuiSwitch-track': {
                                                backgroundColor:
                                                    '#336F89 !important'
                                            }
                                        }}
                                    />
                                }
                                checked={checked}
                                label={`${checked ? '開啟' : '隱藏'}無資料欄位`}
                            />
                        </FormGroup>
                    </Grid>
                </Grid>
                <div>
                    <TextOutput
                        data={outputData}
                        checked={checked}
                        setOutputData={setOutputData}
                        outputData={outputData}
                    />
                </div>
            </Grid>
        </Grid>
    );
}
