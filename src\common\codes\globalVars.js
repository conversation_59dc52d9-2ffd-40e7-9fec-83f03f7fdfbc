export const GV = {
    // Person class name
    PERSON_CLASS_NAME: 'Person',

    // protege property 相關
    DATE_EVENT: 'DateEvent',
    PERSON: 'Person',
    DATE_PROPERTY_STARTS: 'display',
    DATE_PROPERTY_ENDS: 'Date',

    // property 'hide' 隱藏 Person 或 Organization
    defaultHideObj: {
        graph: 'control',
        property: 'hide',
        values: '0'
    },
    FEATURE_HIDE_VALUE_HIDE: '1',
    FEATURE_HIDE_VALUE_UN_HIDE: '0',
    FEATURE_HIDE_PROPERTY: 'hide',
    FEATURE_HIDE_GRAPH: 'control',

    // 人物基本資料的顯示及排序
    orderBasicInfo: [
        'bestKnownName',
        'gender',
        'hasNativePlace',
        'hasBirthDate',
        'hasDeathDate',
        'occupation',
        'originalName',
        'penName',
        'joinPenName',
        'zi',
        'hao'
    ],

    // 系統使用的特殊前綴
    SYSTEM_PREFIX: '_',
    // 特殊的 property，不需顯示
    SYSTEM_HIDE_PROPERTY: ['hide', 'Source Dataset']
};
