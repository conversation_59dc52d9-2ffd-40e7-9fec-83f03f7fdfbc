import React, { useContext, useState, useMemo } from 'react';
import { Box } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Grid from '@mui/system/Unstable_Grid';
import AddNewDataModal from './AddNewDataModal';
import Button from './drawer/Button';
import TextInput from './TextInput';
import { isEmpty } from '../../../common/codes';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';
import { clearAll } from '../utils';

export default function ChatgptLeft({
    isLoading,
    setIsLoading,
    errorMessage,
    setErrorMessage,
    authorMenu
    // isSaveToWorkArea,
}) {
    const [state, dispatch] = useContext(StoreContext);
    const { isChatgptExtract, pressSavedToWorkAreaButtonTimes, status, serialNumber, chatgptDataset, selectedDataset } =
        state.chatgpt;
    const [openModal, setOpenModal] = useState(false);

    const placeholderText = useMemo(
        () => (
            <span
                style={{
                    fontWeight: '600',
                    color: pressSavedToWorkAreaButtonTimes > 0 && !selectedDataset && '#D14835'
                }}
            >
                資料集選擇
                <span style={{ color: '#D14835' }}>*</span>
            </span>
        ),
        [pressSavedToWorkAreaButtonTimes, selectedDataset]
    );

    const addNewData = () => {
        // 尚未擷取過，無須跳出確認modal
        if (!isChatgptExtract && status === '') return;
        // 擷取過，但還沒有獲得狀態及編號時，需跳出確認modal
        if (isEmpty(status) && isEmpty(serialNumber)) {
            setOpenModal(true);
            return;
        }
        clearAll(dispatch);
    };

    return (
        <Grid xs className="chatgpt_left">
            <Grid className="chatgptHeader-left" sx={{ minHeight: '48px' }}>
                <Box sx={{ display: 'flex' }}>
                    <Box sx={{ flexBasis: '120px' }}>
                        <Button
                            clickFunction={() => {
                                addNewData();
                            }}
                            isDisabled={false}
                            buttonText="新建資料"
                            isConfirmation
                        />
                    </Box>
                    <Box
                        sx={{
                            display: 'flex',
                            alignItems: 'center',
                            flexGrow: 1
                        }}
                    >
                        <Autocomplete
                            size="small"
                            disablePortal
                            options={chatgptDataset}
                            sx={{
                                width: '100%',
                                svg: {
                                    fill:
                                        pressSavedToWorkAreaButtonTimes > 0 && !selectedDataset ? '#D14835' : '#336F89'
                                },
                                '& .MuiOutlinedInput-notchedOutline': {
                                    borderColor: pressSavedToWorkAreaButtonTimes > 0 && !selectedDataset && '#D14835'
                                }
                            }}
                            onChange={(event, newValue) => {
                                dispatch({
                                    type: Act.CHATGPT_SELECTED_DATASET,
                                    payload: newValue
                                });
                            }}
                            value={selectedDataset}
                            renderInput={params => <TextField {...params} label={placeholderText} />}
                            isOptionEqualToValue={(option, value) => option.value === value.value}
                            disabled={!(status === '' || status === 'tempSave' || status === 'reject')}
                        />
                    </Box>
                </Box>
            </Grid>
            <Grid
                sx={{
                    backgroundColor: '#ffffff',
                    padding: '24px 24px 24px 24px',
                    borderRadius: '12px',
                    marginTop: '0.5rem'
                    // height: '640px'
                }}
            >
                <Box
                    sx={{
                        height: '44px',
                        marginBottom: '8px',
                        display: 'flex',
                        justifyContent: 'space-between'
                    }}
                >
                    <Box>
                        <Typography variant="subtitle" sx={{ fontWeight: '600' }}>
                            請貼入純文字
                        </Typography>
                        <Typography variant="body2" color="#757575" lineHeight="20.27px">
                            例如：一篇來自傳略的作家簡介
                        </Typography>
                    </Box>
                    <Box>
                        <Button
                            clickFunction={() => {
                                clearAll(dispatch, { clearNumber: false, clearStatus: false });
                            }}
                            isDisabled={!isChatgptExtract && !(status === 'tempSave' || status === 'reject')}
                            buttonText="清除全部"
                            isConfirmation={false}
                        />
                    </Box>
                </Box>
                <div>
                    <TextInput
                        isLoading={isLoading}
                        errorMessage={errorMessage}
                        setIsLoading={setIsLoading}
                        setErrorMessage={setErrorMessage}
                        authorMenu={authorMenu}
                    />
                </div>
                <AddNewDataModal open={openModal} setOpen={setOpenModal} confirmFct={() => clearAll(dispatch)} />
            </Grid>
        </Grid>
    );
}
