import { isEmpty } from 'lodash';
import { getUser } from '../../api/firebase/realtimeDatabase';
import Act from '../../store/actions';

const handleSignInSuccess = (userInfo, dispatch, history) => {
    const { uid, displayName, email } = userInfo;
    if (uid && (displayName || email)) {
        getUser(uid).then(userData => {
            if (!isEmpty(userData)) {
                dispatch({
                    type: Act.FIREBASE_LOGIN_USER,
                    payload: userData
                });
                history.push('/');
            } else {
                getUser(uid).then(result2 => {
                    dispatch({
                        type: Act.FIREBASE_LOGIN_USER,
                        payload: result2
                    });
                });
            }
        });
    }
};
export default handleSignInSuccess;
