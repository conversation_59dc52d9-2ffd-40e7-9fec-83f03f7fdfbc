// 驗證 email
export const validateEmail = email =>
    String(email)
        .toLowerCase()
        .match(
            /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
        );

const PASSWD_STRENGTH_STR = {
    WEIRD: 'weird',
    WEAK: 'weak',
    OK: 'ok',
    STRONG: 'strong',
    AWESOME: 'awesome'
};

// 驗證密碼
export const validatePassword = value => {
    const check = [];
    check[0] = value.match(/[A-Z]/);
    check[1] = value.match(/[a-z]/);
    check[2] = value.match(/\d+/);
    check[3] = value.match(/[!_.-]/);

    let sum = 0;
    for (let i = 0; i < check.length; i += 1) {
        sum += check[i] ? 1 : 0;
    }

    let strength = PASSWD_STRENGTH_STR.WEIRD;
    let pass = false;
    switch (sum) {
        case 0:
            //
            break;
        case 1:
            strength = PASSWD_STRENGTH_STR.WEAK;
            break;
        case 2:
            strength = PASSWD_STRENGTH_STR.OK;
            break;
        case 3:
            strength = PASSWD_STRENGTH_STR.STRONG;
            break;
        case 4:
            strength = PASSWD_STRENGTH_STR.AWESOME;
            break;
        default:
            //
            break;
    }
    if (value && value.length >= 8 && check[0] && check[1] && check[2]) {
        pass = true;
    }
    return { strength, pass };
};
