import React, { useContext, useEffect, useMemo } from 'react';
import axios from 'axios';
import { Route, Switch } from 'react-router-dom';
import routes from '../App-route';
import RouteProtectedHoc from './RouteProtectedHoc';
import { Api } from '../api/Api';
import role from '../App-role';
import LoadingPage from '../pages/LoadingPage';
import NotFound from '../pages/NotFound';
import Act from '../store/actions';
import { StoreContext } from '../store/StoreProvider';

const RouterManager = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { user, main } = state;
    const { authFinished } = main;
    const { chatGptIsLoading } = state.chatgpt;
    const safeRole = useMemo(
        () => (user && user.role ? user.role : role.anonymous),
        [user?.role]
    );

    useEffect(() => {
        if (!authFinished) return;
        const getDatasetList = async () => {
            await axios.get(Api.getDatasetList).then(res => {
                dispatch({
                    type: Act.CHATGPT_DATASET,
                    payload: res?.data?.data
                });
            });
        };
        getDatasetList();
    }, [authFinished]);

    if (!authFinished || chatGptIsLoading) {
        return <LoadingPage />;
    }

    return (
        <Switch>
            {routes &&
                routes.map(route => {
                    if (route.authority.includes(safeRole) && route.public) {
                        if (route.routeComponent) {
                            const RouteComponent = route.routeComponent;
                            return (
                                <RouteComponent
                                    exact
                                    key={route.id}
                                    path={route.path}
                                    component={route.component}
                                />
                            );
                        }

                        return (
                            <Route
                                exact
                                key={route.id}
                                path={route.path}
                                component={route.component}
                            />
                        );
                    }
                    return (
                        <RouteProtectedHoc
                            exact
                            path={route.path}
                            public={route.public}
                            authority={route.authority}
                            component={route.component}
                            safeRole={safeRole}
                            userInfo={user}
                            key={route.id}
                        />
                    );
                })}
            <Route component={NotFound} />
        </Switch>
    );
};

export default RouterManager;
