import React, { useState, useEffect, useContext } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import { Link, useLocation } from 'react-router-dom';

import itemConfig from '../../api/config/config-localStorage';
import { menus } from '../../App-header';
import SignOutButton from '../../common/components/SignOutButton/SignOutButton';
import { StoreContext } from '../../store/StoreProvider';
import './header.scss';

const Header = () => {
    // store
    // const [state, _] = useContext(StoreContext);
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { role, emailVerified } = user;
    // get isLogin from localStorage
    const isLogin = JSON.parse(localStorage.getItem(itemConfig.isLogin));
    const location = useLocation();
    const [activePage, setActivePage] = useState('');

    // const history = useHistory();

    // const handleClickLogo = () => {
    //     // 點擊header上的logo導回首頁
    //     history.push('/');
    // };

    const currentURL = window.location.href;

    useEffect(() => {
        const findPage = menus.menuLeft.find(
            menu => menu.path === location.pathname
        );

        // headerItem設置下底線
        if (findPage) {
            setActivePage(findPage.id);
        }
    }, [role, isLogin, user, currentURL]);

    return (
        <div className="Header">
            <div className="headerBox">
                {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events,jsx-a11y/no-static-element-interactions */}
                <div className="logoBox">
                    {/* <img src={logo} alt="" className="w-100" /> */}
                    <Link
                        to="/"
                        className="logoBox-title"
                        style={{ textDecoration: 'none' }}
                    >
                        ChatGPT NER pilot
                    </Link>
                </div>
                <div className="headerItems">
                    <Box className="workArea">
                        <Link to="/WorkArea" style={{ textDecoration: 'none' }}>
                            <Box className="workArea-box">
                                <p>我的工作區</p>
                            </Box>
                        </Link>
                    </Box>
                    {/*= == header items 區塊 === */}
                    <Box className="Box">
                        {emailVerified
                            ? menus &&
                              menus.menuLeft &&
                              menus.menuLeft
                                  // .filter((menu) => allowGetIn(menu, role))
                                  .map(item => (
                                      <Link
                                          to={item.path}
                                          key={item.id}
                                          style={{ textDecoration: 'none' }}
                                      >
                                          <div className="HeaderItem">
                                              <Button
                                                  variant="text"
                                                  className={
                                                      activePage === item.id
                                                          ? 'textPrimaryColor textTransform'
                                                          : 'textTransform'
                                                  }
                                                  onClick={() =>
                                                      setActivePage(item.id)
                                                  }
                                              >
                                                  {item.name}
                                              </Button>
                                              {activePage === item.id && (
                                                  <div className="hrLine" />
                                              )}
                                          </div>
                                      </Link>
                                  ))
                            : null}
                    </Box>
                    {/*= == header SignIn/SignOut 區塊 === */}
                    <Box className="Box">
                        {menus &&
                            menus.menuRight &&
                            menus.menuRight
                                // .filter((menu) => allowGetIn(menu, role))
                                .filter(menu =>
                                    // allowGetIn(menu, role) &&
                                    isLogin
                                        ? menu.showOnLogin
                                        : !menu.showOnLogin
                                )
                                .map(item => {
                                    if (item.showOnLogin) {
                                        return (
                                            <div
                                                className="HeaderItem"
                                                key={item.id}
                                            >
                                                <SignOutButton
                                                    user={user}
                                                    setActivePage={
                                                        setActivePage
                                                    }
                                                    menus={menus}
                                                    item={item}
                                                />
                                            </div>
                                        );
                                    }
                                    return (
                                        <Link
                                            to={item.path}
                                            key={item.id}
                                            style={{ textDecoration: 'none' }}
                                        >
                                            <div className="HeaderItem">
                                                <Button
                                                    variant="text"
                                                    className="textTransform signInStyle"
                                                    onClick={() =>
                                                        setActivePage('')
                                                    }
                                                >
                                                    {item.name}
                                                </Button>
                                            </div>
                                        </Link>
                                    );
                                })}
                    </Box>
                </div>
            </div>
        </div>
    );
};

export default Header;
