import React, { useEffect, useState } from 'react';

// ui

// common
import TextField from '@mui/material/TextField';
import { isEmpty, isNotEmpty, isNumeric } from '../../../../common/codes';

// custom

const CustomDateInput = ({ onchangeFct, i, k, data }) => {
    const [inputDate, setInputDate] = useState('');

    const handleChange = event => {
        //
        const newValue = event.target.value.replaceAll('-', '');
        //
        if (isEmpty(newValue)) {
            setInputDate('');
            return;
        }
        // console.log(value);
        if (isNotEmpty(newValue) && isNumeric(newValue)) {
            //
            const yyyy = newValue.slice(0, 4);
            const MM = newValue.slice(4, 6);
            const dd = newValue.slice(6, 8);
            //
            let dataFormatValue = '';
            //
            if (isNotEmpty(yyyy)) {
                const currYear = new Date().getFullYear();
                if (yyyy > currYear) {
                    dataFormatValue += currYear;
                } else {
                    dataFormatValue += yyyy;
                }
            }
            if (isNotEmpty(MM)) {
                if (MM > 12) {
                    dataFormatValue += '-12';
                } else if (MM < 0) {
                    dataFormatValue += '-00';
                } else {
                    dataFormatValue += `-${MM}`;
                }
            }
            if (isNotEmpty(dd)) {
                //
                const date = new Date();
                const daysInMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
                //
                if (dd > daysInMonth) {
                    dataFormatValue += `-${daysInMonth}`;
                } else if (dd < 0) {
                    dataFormatValue += '-00';
                } else {
                    dataFormatValue += `-${dd}`;
                }
            }
            //
            setInputDate(dataFormatValue);
            onchangeFct(i, k, dataFormatValue);
        }
    };

    useEffect(() => {
        setInputDate(data);
    }, [data]);

    return (
        <TextField
            fullWidth
            variant="standard"
            value={inputDate}
            onChange={handleChange}
            InputProps={{
                disableUnderline: true,
                sx: {
                    lineHeight: '20.27px',
                    borderRadius: '8px',
                    border: '1px solid #EEEEEE',
                    padding: '8px 12px',
                    fontSize: '14px',
                    '& .MuiInput-input': {
                        padding: '0',
                        margin: '0',
                        fontFamily: '"Source Han Serif TC", sans-serif'
                    }
                }
            }}
        />
    );
};

export default CustomDateInput;
