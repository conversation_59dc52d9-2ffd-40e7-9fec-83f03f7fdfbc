import React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import commentDisabledIcon from '../../../images/chatgpt/comment-disabled.svg';
import commentIcon from '../../../images/chatgpt/comment.svg';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '40%',
    minHeight: '35%',
    bgcolor: 'background.paper',
    borderRadius: '12px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between'
};

const CommentModal = ({ comment = '', disabled = false }) => {
    const [open, setOpen] = React.useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    return (
        <>
            <Button onClick={handleOpen} sx={{ width: '24px', height: '24px', minWidth: 0 }} disabled={disabled}>
                <img src={disabled ? commentDisabledIcon : commentIcon} alt="comment" />
            </Button>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={style}>
                    <Typography
                        id="modal-modal-title"
                        sx={{
                            padding: '16px 24px',
                            bgcolor: '#F4F8FB',
                            color: '#336F89',
                            borderBottom: '1px solid #DAE9EF',
                            borderRadius: '12px 12px 0 0',
                            fontWeight: 600,
                            fontSize: '20px'
                        }}
                    >
                        審核評論
                    </Typography>
                    <Typography
                        id="modal-modal-description"
                        sx={{ padding: '24px', flex: 1, fontWeight: 400, fontSize: '14px' }}
                    >
                        {comment}
                    </Typography>
                    <Box sx={{ padding: '0 24px 24px 24px', display: 'flex', justifyContent: 'end' }}>
                        <Button
                            onClick={handleClose}
                            sx={{
                                bgcolor: '#FAFAFA',
                                padding: '8px 16px',
                                color: '#757575',
                                fontWeight: 600,
                                fontSize: '14px',
                                minWidth: 0
                            }}
                        >
                            關閉
                        </Button>
                    </Box>
                </Box>
            </Modal>
        </>
    );
};

export default CommentModal;
