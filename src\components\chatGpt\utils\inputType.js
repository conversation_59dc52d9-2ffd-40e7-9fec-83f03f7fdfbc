const dateType = [
    'hasBirthDate__DateEvent',
    'hasDeathDate__DateEvent',
    'hasStartDate__DateEvent',
    'hasEndDate__DateEvent',
    'hasInceptionDate__DateEvent'
];

const multiType = [
    // Person
    'illness__string',
    'occupation__string',
    'researchArea__string',

    // NameNode
    'zi__string',
    'hao__string',
    'penName__string',
    'joinPenName__string',
    // EmploymentEvent
    'hasAlumni__Person',
    'hasGenre__string',
    'hasColumnist__Person',
    'penName__string',
    // Publication
    'hasGenre__string',
    'hasAuthor__Person',
    'hasDescribedTarget__Person',
    'hasEditor__Person',
    'hasAuthor__Person',
    'hasBookPrefacer__Person',
    'hasBookIncription__Person',
    'hasBookTitle__Person',
    'hasContributor__Person',
    'hasTranslator__Person',
    // Article
    'hasAuthor__Person',
    'hasDescribedTarget__Person',
    'hasGenre__string',
    'hasTranslator__Person',
    'owner__string',
    'penName__string',
    // OtherWork
    'hasRelatedPerson__Person',
    // relationevent
    'hasAdvisor__Person',
    'hasBiaoSyong__Person',
    'hasBrother__Person',
    'hasBrotherInLaw__Person',
    'hasClassmate__Person',
    'hasColleague__Person',
    'hasCousin__Person',
    'hasDaughter__Person',
    'hasDescendants__Person',
    'hasFather__Person',
    'hasFatherInLaw__Person',
    'hasFirstDaughter__Person',
    'hasFirstSon__Person',
    'hasFriend__Person',
    'hasFriendship__Person',
    'hasGrandFather__Person',
    'hasGrandMother__Person',
    'hasGrandSon__Person',
    'hasGreatGrandSon__Person',
    'hasHired__Person',
    'hasHusband__Person',
    'hasIntroducer__Person',
    'hasKinship__Person',
    'hasKnown__Person',
    'hasMother__Person',
    'hasMotherGrandSon__Person',
    'hasNephew__Person',
    'hasOlderSister__Person',
    'hasParticipatePolitics__Person',
    'hasProceededWith__Person',
    'hasSon__Person',
    'hasSonInLaw__Person',
    'hasSpouse__Person',
    'hasStaffMemberWas__Person',
    'hasStudent__Person',
    'hasSubordinateWas__Person',
    'hasSupportedPolitics__Person',
    'hasTeacher__Person',
    'hasTeacherStudent__Person',
    'hasWife__Person',
    'hasWorkFor__Person',
    'hasYaji__Person',
    'hasYoungerBrother__Person',
    'hasYoungerSister__Person',
    'relationRemarks__string',

    // Event
    'hasRelatedPerson__Person',
    // awardevent
    'wasConferredBy__Person'
];

export { dateType, multiType };
