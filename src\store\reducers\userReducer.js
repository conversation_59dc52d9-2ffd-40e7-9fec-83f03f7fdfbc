import Act from '../actions';

const initState = { token: null };

// eslint-disable-next-line default-param-last
const userReducer = (state = initState, action) => {
    switch (action?.type) {
        case Act.FIREBASE_LOGIN_USER:
            return { ...state, ...action.payload };
        case Act.FIREBASE_LOGOUT_USER:
            return {};
        case Act.FIREBASE_USER_TOKEN:
            return { ...state, token: action.payload };
        default:
            return state;
    }
};

export default userReducer;
