body{
  margin:0;
}

//介面主色:湖綠色
$colorPrimary: #2CADCA;
$colorBlack:#222222;
$colorWhite:#FFFFFF;

.Header {
  //height: 93px;
  width: 100%;
  //height: 7vh;
  display: flex;
  border: 1px solid #DAE9EF;
  .headerBox{
    width: 100%;
    //max-width: 1136px;
    //padding-left:10px;
    //padding-right:10px;
    //margin:1rem auto 0 auto;
    padding: 16px 80px;
    display: flex;
    justify-content: space-between;
    align-items:center;
    background-color: #F9FAFC;
    .logoBox{
      //cursor: pointer;
      max-width:240px;
      &-title{
        color: #336F89;
        font-size: 24px;
        font-weight: 600;
      }
      .w-100{
        width: 100%;
      }
    }
    .headerItems{
      display:flex;
      flex-direction: row;
      .Box {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        text-align: center;
        height: 80%;
        .HeaderItem {
          //min-width: 100px;
          padding:0 7px;
          height: 100%;
          button {
            width: 100%;
            cursor: pointer;
            color: $colorBlack;
          }
        }
        .hrLine {
          height: 3px;
          width: 100%;
          background-color: $colorPrimary
        }
      }
      .workArea{
        display: flex;
        align-items: center;
        justify-content: center;
        &-box{
          border: 1px solid #DAE9EF;
          padding: 8px 16px;
          border-radius: 8px;
        }
        p{
          color: #336F89;
          font-weight: 600;
          font-size: 14px;
        }
      }
    }
  }
}

.textPrimaryColor{
  color:$colorPrimary !important;
}

.textTransform{
  text-transform: none !important;
}

.signInStyle{
  background-color: $colorPrimary !important;
  color:$colorWhite !important;
  font-weight:400 !important;
  cursor: pointer;
}
