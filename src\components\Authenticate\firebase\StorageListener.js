import { useContext, useEffect } from 'react';
import { getSwgJson } from '../../../api/firebase/storage';
import { StoreContext } from '../../../store/StoreProvider';

const StorageListener = () => {
    const [state, _] = useContext(StoreContext);
    const { main } = state;
    const { swgJSON } = main;

    // get webStyle setting
    useEffect(() => {
        if (!swgJSON.storage) return;
        getSwgJson(swgJSON.storage).then(urltoken => {
            // console.log("urltoken", urltoken);

            // todo: use fetch or axios to get json file
            fetch(urltoken, { mode: 'no-cors' })
                .then(res =>
                    // console.log("res", res);
                    res.blob()
                )
                .then(() => {
                    // console.log("data", data);
                })
                .catch(err => {
                    console.log(err);
                });
        });
    }, [swgJ<PERSON><PERSON>]);

    return null;
};

export default StorageListener;
