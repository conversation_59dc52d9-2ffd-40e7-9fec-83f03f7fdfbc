import React from 'react';
import { Tabs, Tab } from '@mui/material';

const CustomTabs = ({ tabs, tabId, setTabId }) => {
    const handleChange = (e, newValue) => {
        setTabId(newValue);
    };

    return (
        <Tabs
            value={tabId}
            onChange={handleChange}
            TabIndicatorProps={{ style: { display: 'none' } }}
            sx={{
                padding: '6px',
                border: '1px solid #E9F1F5',
                borderRadius: '12px',
                '& .MuiTabs-flexContainer': {
                    gap: '4px'
                }
            }}
        >
            {tabs.map(({ id, name }) => (
                <Tab
                    key={id}
                    value={id}
                    label={name}
                    sx={{
                        borderRadius: '8px',
                        backgroundColor:
                            tabId === id ? '#F4F8FB' : 'transparent',
                        color: '#336F89',
                        fontWeight: 600,
                        '&:hover': {
                            backgroundColor: '#F3F7FA'
                        },
                        '&.Mui-selected': {
                            color: '#336F89'
                        }
                    }}
                />
            ))}
        </Tabs>
    );
};

export default CustomTabs;
