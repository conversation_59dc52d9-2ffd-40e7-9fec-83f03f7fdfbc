import React, { useCallback } from 'react';
import { Autocomplete, TextField } from '@mui/material';
import { createFilterOptions } from '@mui/material/Autocomplete';

const OptimizedAutocomplete = React.memo(({
    options,
    handleUserSelect,
    kIndex,
    handleDropdownSelect,
    selectedValue
}) => {
    const filterOptions = createFilterOptions({
        matchFrom: 'any',
        limit: 500
    });

    const handleChange = useCallback((event, newValue) => {
        handleUserSelect(kIndex);
        handleDropdownSelect(newValue);
    }, [handleUserSelect, handleDropdownSelect, kIndex]);

    const placeholderText = (
        <span style={{ fontWeight: '600' }}>
            選擇...
            <span style={{ color: 'red' }}>*</span>
        </span>
    );

    return (
        <Autocomplete
            size="small"
            disablePortal
            options={options || []}
            sx={{
                width: '100%',
                svg: { fill: '#336F89' }
            }}
            onChange={handleChange}
            getOptionLabel={option => {
                if (!option) return '';
                if (typeof option === 'string') return option;
                return option.label || '';
            }}
            value={selectedValue}
            renderInput={params => <TextField {...params} label={placeholderText} />}
            filterOptions={filterOptions}
            isOptionEqualToValue={(option, value) => {
                if (!option || !value) return false;
                return option.id === value.id;
            }}
            renderOption={(props, option) =>
                option?.label ? (
                    <li {...props} key={option.id || option.label}>
                        {option.label}
                    </li>
                ) : null
            }
        />
    );
});

export default OptimizedAutocomplete;
