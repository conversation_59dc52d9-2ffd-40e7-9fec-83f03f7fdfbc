{
    "root": true,
    "env": {
        "browser": true,
        "es2021": true,
        "commonjs": true
    },
    "extends": [
        "eslint:recommended",
        "plugin:react/recommended",
        "plugin:react/jsx-runtime",
        "plugin:react-hooks/recommended",
        "plugin:jsx-a11y/recommended",
        "plugin:import/recommended",
        "airbnb",
        "prettier"
    ],
    "parserOptions": {
        "ecmaFeatures": {
            "jsx": true
        },
        "ecmaVersion": "latest",
        "sourceType": "module"
    },
    "settings": {
        "react": {
            "version": "detect"
        },
        "import/resolver": {
            "node": {
                "extensions": [".js", ".jsx", ".ts", ".tsx", ".css", ".scss"]
            }
        }
    },
    "plugins": [
        "react",
        "react-hooks",
        "jsx-a11y",
        "import",
        "unused-imports",
        "prettier"
    ],
    "rules": {
        // React 規則
        "react/prop-types": "warn",
        "react/require-default-props": "off",
        "react/jsx-filename-extension": ["warn", { "extensions": [".js", ".jsx"] }],
        "react/function-component-definition": ["warn", {
            "namedComponents": "arrow-function",
            "unnamedComponents": "arrow-function"
        }],
        "react/no-unknown-property": "error",
        "react/jsx-props-no-spreading": "warn",

        // Hooks 規則
        "react-hooks/rules-of-hooks": "error",
        "react-hooks/exhaustive-deps": "warn",

        // 導入相關規則
        "import/no-unresolved": "error",
        "import/order": [
            "warn",
            {
                "groups": [
                    ["builtin", "external"],
                    "internal",
                    ["parent", "sibling", "index"]
                ],
                "alphabetize": {
                    "order": "asc",
                    "caseInsensitive": true
                },
                "pathGroups": [
                    {
                        "pattern": "react",
                        "group": "builtin",
                        "position": "before"
                    }
                ],
                "pathGroupsExcludedImportTypes": ["builtin"]
            }
        ],
        "import/no-extraneous-dependencies": ["error", {
            "devDependencies": [
                "**/*.test.js",
                "**/*.spec.js",
                "**/test/**/*.js"
            ]
        }],
        "unused-imports/no-unused-imports": "error",
        "import/prefer-default-export": "off",

        "jsx-a11y/alt-text": "error",
        "jsx-a11y/aria-role": "error",

        "no-console": ["warn", { "allow": ["warn", "error"] }],
        "no-unused-vars": ["error", {
            "argsIgnorePattern": "^_",
            "varsIgnorePattern": "^_",
            "ignoreRestSiblings": true
        }],
        "prefer-const": "error",
        "no-duplicate-imports": "error",
        "max-len": ["warn", {
            "code": 120,
            "ignoreComments": true,
            "ignoreUrls": true,
            "ignoreStrings": true,
            "ignoreTemplateLiterals": true
        }],

        "prettier/prettier": ["warn", {
            "singleQuote": true,
            "trailingComma": "none",
            "tabWidth": 4,
            "printWidth": 120,
            "semi": true,
            "arrowParens": "avoid",
            "endOfLine": "auto"
        }]
    }
}