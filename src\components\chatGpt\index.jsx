import React, { useContext, useEffect, useRef, useState, useCallback } from 'react';
import { Box, Button as MuiButton } from '@mui/material';
import axios from 'axios';
import { Base64 } from 'js-base64';
import { Link } from 'react-router-dom';
import ChatgptLeft from './subComponemts/ChatgptLeft';
import ChatgptRight from './subComponemts/ChatgptRight';
import Button from './subComponemts/drawer/Button';
import WriterPickerModal from './subComponemts/WriterPickerModal';
import { fetchCoordinates } from './utils';
import authors from './utils/authors';
import { postEntry, deleteGraph } from './utils/entry';
import { Api } from '../../api/Api';
import backIcon from '../../images/chatgpt/back.svg';
import Act from '../../store/actions';
import { StoreContext } from '../../store/StoreProvider';
import './Chatgpt.scss';

const Chatgpt = () => {
    const [state, dispatch] = useContext(StoreContext);
    const {
        isChatgptExtract,
        pressSavedToWorkAreaButtonTimes,
        authorUpdateDraft,
        inputText,
        outputData,
        selectedDataset,
        isSaveInWorkArea,
        serialNumber
    } = state.chatgpt;
    const { user } = state;
    const reviewStatus = state.chatgpt.status;
    const [isLoading, setIsLoading] = useState(false);
    const [checked, setChecked] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [authorMenu, setAuthorMenu] = useState('');
    const [writerModalOpen, setWriterModalOpen] = useState(false);
    const [writerOptions, setWriterOptions] = useState([]);
    const [writerPickerModalType, setWriterPickerModalType] = useState('temp');
    const [isModalLoading, setIsModalLoading] = useState(false);

    const modifiedOutputData = useRef([]);

    const modifyOutputData = (origin, update) => {
        const stripSuffix = key => key.split('__')[0];
        const transformData = (data, replaceData) =>
            data.map(section => {
                if (section.sub) {
                    const newSub = section.sub.map(item => {
                        const newItem = { ...item };
                        Object.keys(item).forEach(key => {
                            const baseKey = stripSuffix(key);
                            if (Object.prototype.hasOwnProperty.call(replaceData, baseKey)) {
                                // 原本無資料則無需修改
                                if (newItem[key].value === '' && key !== 'bestKnownName') return;

                                newItem[key] = {
                                    ...item[key],
                                    value: replaceData[baseKey] || item[key].value
                                };
                            }
                        });
                        return newItem;
                    });
                    return { ...section, sub: newSub };
                }
                return section;
            });

        return transformData(origin, update);
    };

    const handleSubmitReview = async status => {
        if (!user?.displayName) return;

        const foundBestKnownName =
            modifiedOutputData.current
                .find(el => el.titleEn === 'BasicInformation')
                ?.sub?.find(el => el.bestKnownName?.value) ?? null;

        if (!selectedDataset) {
            alert('請選擇資料集');
            return;
        }

        if (!foundBestKnownName) {
            alert('未填入常見名稱');
            return;
        }

        dispatch({ type: Act.CHATGPT_EXTRACT, payload: false });

        let graph = '';
        let reviewId = '';
        const reviewTime = `${Date.now()}`;

        if (serialNumber) {
            reviewId = serialNumber;
            graph = Base64.encode(encodeURI(serialNumber));
            await deleteGraph(graph);
        } else {
            reviewId = `${user.displayName}_${reviewTime}`;
            graph = Base64.encode(encodeURI(reviewId));
        }

        dispatch({ type: Act.CHATGPT_NUMBER, payload: reviewId });
        dispatch({ type: Act.CHATGPT_STATUS, payload: status });

        postEntry(inputText, modifiedOutputData.current, selectedDataset, user, status, graph, reviewId, reviewTime);
    };

    useEffect(() => {
        const getWriterList = async () => {
            await axios.get(Api.getWriterList).then(res => {
                setWriterOptions(res?.data?.data);
            });
        };

        getWriterList();
    }, []);

    useEffect(() => {
        const cloneOutputData = structuredClone(outputData);

        modifiedOutputData.current = modifyOutputData(cloneOutputData, authorUpdateDraft);
    }, [authorUpdateDraft]);

    useEffect(() => {
        const loadCoords = async () => {
            await fetchCoordinates(dispatch);
        };

        loadCoords();
    }, []);

    const handleModalOpen = useCallback(async (type) => {
        if (isModalLoading) return;
        setIsModalLoading(true);
        dispatch({
            type: Act.CHATGPT_PRESS_SAVED_TO_WORK_AREA_BUTTON_TIMES,
            payload: pressSavedToWorkAreaButtonTimes + 1
        });

        if (!selectedDataset) {
            setIsModalLoading(false);
            return;
        }

        setWriterPickerModalType(type);
        setWriterModalOpen(true);

    }, [isModalLoading, selectedDataset, pressSavedToWorkAreaButtonTimes, dispatch]);

    return (
        <Box>
            <Box className="chatgpt">
                <Box className="chatgpt_container" sx={{ display: 'flex', width: '100%', gap: '1rem' }}>
                    <ChatgptLeft
                        isLoading={isLoading}
                        setIsLoading={setIsLoading}
                        setErrorMessage={setErrorMessage}
                        errorMessage={errorMessage}
                        authorMenu={authorMenu}
                    />
                    <ChatgptRight
                        authors={authors}
                        setChecked={setChecked}
                        checked={checked}
                        setAuthorMenu={setAuthorMenu}
                    />
                </Box>
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'flex-between',
                        width: '100%',
                        marginTop: '1rem'
                    }}
                >
                    {isSaveInWorkArea && (
                        <Box
                            sx={{
                                display: 'flex',
                                width: '100%',
                                marginTop: '1rem',
                                gap: '1rem'
                            }}
                        >
                            <Link to="/WorkArea">
                                <MuiButton
                                    sx={{
                                        backgroundColor: 'transparent',
                                        padding: '8px 12px',
                                        border: '1px solid #DAE9EF',
                                        color: '#336F89',
                                        fontSize: '14px',
                                        fontWeight: '600',
                                        borderRadius: '8px',
                                        minWidth: '150px'
                                    }}
                                >
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            alignItems: 'center',
                                            gap: '0.5rem'
                                        }}
                                    >
                                        <p>
                                            <img src={backIcon} alt="back" />
                                        </p>
                                        <p> 返回我的工作區</p>
                                    </Box>
                                </MuiButton>
                            </Link>
                        </Box>
                    )}

                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            width: '100%',
                            marginTop: '1rem',
                            gap: '1rem'
                        }}
                    >
                        <Button
                            clickFunction={() => handleModalOpen('temp')}
                            isDisabled={
                                isModalLoading ||
                                (!isChatgptExtract && !(reviewStatus === 'tempSave' || reviewStatus === 'reject'))
                            }
                            buttonText="暫存至我的工作區"
                            isConfirmation={false}
                        />
                        <Button
                            clickFunction={() => handleModalOpen('save')}
                            isDisabled={
                                isModalLoading ||
                                (!isChatgptExtract && !(reviewStatus === 'tempSave' || reviewStatus === 'reject'))
                            }
                            buttonText="送出審核"
                            isConfirmation
                        />
                    </Box>
                </Box>

                <WriterPickerModal
                    open={writerModalOpen}
                    setOpen={setWriterModalOpen}
                    confirmFct={() => {
                        // eslint-disable-next-line no-unused-expressions
                        writerPickerModalType === 'save'
                            ? handleSubmitReview('waiting')
                            : handleSubmitReview('tempSave');
                    }}
                    options={writerOptions}
                    outputData={outputData}
                    writerPickerModalType={writerPickerModalType}
                    onClose={() => {
                        setIsModalLoading(false);
                    }}
                />
            </Box>
        </Box>
    );
};

export default Chatgpt;
