// import { bs64Decode, bs64Encode } from "./index";
// import { FUSEKI_ON } from "../../api/hkbdb/Api";
// import {
//     URI_ENCODE_TYPE,
//     CREATE_NNI_TYPE,
//     ALL_CLASS
// } from "../../config/config-ontology";
//
// // https://stackoverflow.com/questions/7449588/why-does-decodeuricomponent-lock-up-my-browser
// // 單獨 % 的情況需要另外處理
// export const decodeURIComponentSafe = s => {
//     if (!s) {
//         return s;
//     }
//
//     return decodeURIComponent(
//         s.replace(/%(?![0-9a-fA-F][0-9a-fA-F]+)/g, "%25")
//     );
// };
//
// // 檢查 uri 是否已經 encodeURI
// export const isEncoded = uri => {
//     const tmpUri = uri || "";
//
//     return tmpUri !== decodeURIComponentSafe(tmpUri);
// };
//
// // 部分字元無法透過 encodeURI encode,使用特殊轉換
// function fixedEncodeURIComponent(str) {
//     return encodeURIComponent(str).replace(
//         /[!'()*]/g,
//         c => `%${c.charCodeAt(0).toString(16)}`
//     );
// }
//
// // 可以依據 fuseki 開啟狀態來 uriEncode id(on/off) & bs64encode(on)
// // 使用 fuseki 資料庫(所有 id 已經 uriEncode)
// // 6YeR5bq4 => 金庸 => %E9%87%91%E5%BA%B8 => JUU5JTg3JTkxJUU1JUJBJUI4
// // 使用 stardog 資料庫(所有 id 沒有 uriEncode)
// // 6YeR5bq4 => 金庸 => 6YeR5bq4
// export const bs64EncIdToUriEncBs64EncId = str => {
//     const decodeStr = bs64Decode(str || "");
//     // 使用舊的資料庫
//     if (!FUSEKI_ON) {
//         return bs64Encode(decodeStr);
//     }
//     // 使用新的資料庫
//     // 已經 encode, 就不用再 uriEncode, 直接 bs64Encode
//     if (isEncoded(decodeStr)) {
//         return bs64Encode(decodeStr);
//     }
//     // 尚未 encode, 執行 uriEncode, 然後 bs64Encode
//     return bs64Encode(fixedEncodeURIComponent(decodeStr));
// };
//
// // 可以依據 fuseki 開啟狀態來 uriEncode id(on/off)
// // #使用 fuseki 資料庫(所有 id 已經 uriEncode)
// // 6YeR5bq4 => 金庸 => %E9%87%91%E5%BA%B8
// // #使用 stardog 資料庫(所有 id 沒有 uriEncode)
// // 6YeR5bq4 => 金庸
// export const bs64EncIdToUriEncId = str => {
//     const decodeStr = bs64Decode(str || "");
//     // 使用舊的資料庫
//     if (!FUSEKI_ON) {
//         return decodeStr;
//     }
//     // 使用新的資料庫
//     // 已經 encode, 就不用在 uriEncode
//     if (isEncoded(decodeStr)) {
//         return decodeStr;
//     }
//     // 尚未 encode, 執行 uriEncode
//     return fixedEncodeURIComponent(decodeStr);
// };
//
// // 可以依據 fuseki 開啟狀態來 uriEncode id
// // # 使用 fuseki 資料庫(所有 id 已經 uriEncode)
// // 6YeR5bq4 => 金庸 => %E9%87%91%E5%BA%B8
// // # 使用 stardog 資料庫(所有 id 沒有 uriEncode)
// // 6YeR5bq4 => 金庸
// export const idToUriEncId = str => {
//     const tmpStr = str || "";
//     // 使用舊的資料庫
//     if (!FUSEKI_ON) {
//         return tmpStr;
//     }
//     if (isEncoded(tmpStr)) {
//         return tmpStr;
//     }
//     return fixedEncodeURIComponent(tmpStr);
// };
//
// // 盧燕珊 => %E7%9B%A7%E7%87%95%E7%8F%8A => JUU3JTlCJUE3JUU3JTg3JTk1JUU3JThGJThB
// export const idToUriEncBs64EncId = str => {
//     const tmpStr = str || "";
//     // 使用舊的資料庫
//     if (!FUSEKI_ON) {
//         return bs64Encode(tmpStr);
//     }
//     // 使用新的資料庫
//     // 已經 encode, 就不用再 uriEncode, 直接 bs64Encode
//     if (isEncoded(tmpStr)) {
//         return bs64Encode(tmpStr);
//     }
//     // 尚未 encode, 執行 uriEncode, 然後 bs64Encode
//     return bs64Encode(fixedEncodeURIComponent(tmpStr));
// };
//
// // 將 encode id 轉換成沒有 encode id
// // %E7%9B%A7%E7%87%95%E7%8F%8A => 盧燕珊
// export const uriEncIdToNonEncId = str => {
//     const tmpStr = str || "";
//     // 使用舊的資料庫
//     if (!FUSEKI_ON) {
//         return tmpStr;
//     }
//     if (isEncoded(tmpStr)) {
//         return decodeURIComponentSafe(tmpStr);
//     }
//     return tmpStr;
// };
//
// // base64 encode with prefix id
// const bs64EncodeId = prefixId => {
//     if (!prefixId || prefixId.length <= 3) {
//         return null;
//     }
//     const prefix = `${prefixId}`.slice(0, 3);
//     const shouldUriEncode = URI_ENCODE_TYPE.find(
//         evt => prefix.toLowerCase().indexOf(evt.prefix.toLowerCase()) > -1
//     );
//     const idNumber = `${prefixId}`.slice(3);
//     if (shouldUriEncode) {
//         return `${prefix}${idToUriEncBs64EncId(idNumber)}`;
//     }
//     return `${prefix}${bs64Encode(idNumber)}`;
// };
// export { bs64EncodeId };
//
// // base64 decode with prefix id
// const bs64DecodeId = prefixId => {
//     if (!prefixId || prefixId.length <= 3) {
//         return null;
//     }
//     const prefix = `${prefixId}`.slice(0, 3);
//     const idNumber = `${prefixId}`.slice(3);
//     return `${prefix}${bs64Decode(idNumber)}`;
// };
// export { bs64DecodeId };
//
// /**
//  * 依據 range 取得 Prefix
//  * @param range
//  * @returns {unknown|null}
//  */
// export const getPrefixByRange = range => {
//     if (!range) return null;
//     const find = ALL_CLASS.find(
//         evt => range.toLowerCase().indexOf(evt.eventType.toLowerCase()) > -1
//     );
//     return find?.prefix || null;
// };
//
// /**
//  * 依據 classType 取得 EventType
//  * @param range
//  * @returns {unknown|null}
//  */
// export const getEventTypeByRange = range => {
//     if (!range) return null;
//     const find = ALL_CLASS.find(
//         evt => range.toLowerCase().indexOf(evt.eventType.toLowerCase()) > -1
//     );
//     return find?.eventType || null;
// };
//
// /**
//  * 是否需要 uriEncode
//  * @param range
//  * @returns {{prefix: string, eventType: string}|null}
//  */
// export function doUriEncode(range) {
//     if (!range) return null;
//     const find = URI_ENCODE_TYPE.find(
//         evt => range.toLowerCase().indexOf(evt.eventType.toLowerCase()) > -1
//     );
//     return find || null;
// }
//
// /**
//  * 是否需要建立 nameNode
//  * @param range
//  * @returns {{prefix: string, eventType: string}|null}
//  */
// export function doCreateNNI(range) {
//     if (!range) return null;
//     const find = CREATE_NNI_TYPE.find(
//         evt => range.toLowerCase().indexOf(evt.eventType.toLowerCase()) > -1
//     );
//     return find || null;
// }
