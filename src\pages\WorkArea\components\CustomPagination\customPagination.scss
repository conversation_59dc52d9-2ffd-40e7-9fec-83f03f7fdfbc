.paginationWrapper {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    .paginationContainer {
        grid-column: 2 / 3;
        display: flex;
        justify-content: center;
    }

    .pageIndicator {
        color: #336f89;
        display: flex;
        justify-content: end;
        align-items: center;
        gap: 4px;
    }
}

.pagination .MuiPaginationItem-root {
    padding: 8px 16px;
    border-radius: 40px;
    font-weight: 600;
    font-size: 14px;
    line-height: 20.12px;
}

.pagination .MuiPaginationItem-page.Mui-selected {
    background-color: #336f89;
    color: #fff;
    display: block;
}

.pagination .MuiPaginationItem-page.Mui-selected:hover {
    background-color: #285765;
    color: #fff;
}

.pagination .MuiPaginationItem-previousNext {
    background-color: transparent;
    color: #336f89;
}

.pagination .MuiPaginationItem-page {
    display: none;
}
