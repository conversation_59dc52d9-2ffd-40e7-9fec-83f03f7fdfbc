import React from 'react';
import { Box, But<PERSON>, Modal } from '@mui/material';
import Typography from '@mui/material/Typography';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 480,
    bgcolor: '#FFFFFF',
    p: 4,
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    padding: 0
};

const titleDivStyle = {
    bgcolor: '#F4F8FB',
    padding: '16px 24px',
    color: '#336F89',
    borderRadius: 2,
    '& h2': {
        fontWeight: 600
    }
};

const contentDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '16px 24px',
    borderRadius: 2,
    '& p': {
        fontSize: '14px'
    }
};

const buttonStyle = {
    width: '100%',
    padding: ' 8px 16px',
    backgroundColor: '#336F89',
    marginTop: '1rem',
    color: '#FFFFFF',
    '&:hover': {
        backgroundColor: `rgba(51, 111, 137, 0.9)`
    }
};

const ErrorModal = ({ open, setOpen, title, content }) => {
    const handleClose = () => setOpen(false);

    return (
        <div>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={style}>
                    <Box sx={titleDivStyle}>
                        <Typography id="modal-modal-title" variant="h6" component="h2">
                            {title}
                        </Typography>
                    </Box>
                    <Box sx={contentDivStyle}>
                        <Typography id="modal-modal-description">{content}</Typography>
                        <Button sx={buttonStyle} onClick={handleClose}>
                            好
                        </Button>
                    </Box>
                </Box>
            </Modal>
        </div>
    );
};

export default ErrorModal;
