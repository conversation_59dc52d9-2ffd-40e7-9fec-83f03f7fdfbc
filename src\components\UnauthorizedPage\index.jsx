import React, { useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';
import './UnauthorizedPage.scss';
import { getAuth, signOut } from 'firebase/auth';
import { CircularProgress } from '@mui/material';
import HkbdbUnauthorized from './components/HkbdbUnauthorized';
import Unauthorized from './components/Unauthorized';
import itemConfig from '../../api/config/config-localStorage';
import { isEmpty } from '../../common/codes';
import act from '../../store/actions';
import { StoreContext } from '../../store/StoreProvider';

const UnauthorizedPage = () => {
    const isLogin = JSON.parse(localStorage.getItem(itemConfig.isLogin));
    const history = useHistory();
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    // 暫時拿掉userName
    const [_, setUserName] = useState('');
    const [role, setRole] = useState('');
    const [isLoading, setIsLoading] = useState(true);

    const handleLogout = async () => {
        if (isLogin) {
            const auth = getAuth();
            // SignOut firebase
            await signOut(auth)
                .then(() => {
                    setUserName(user.displayName);
                    // Sign-out successful.
                })
                .catch(error => {
                    // An error happened.
                    console.log(error);
                });
            // clean user data
            dispatch({ type: act.FIREBASE_LOGOUT_USER });
            // clean localStorage
            localStorage.removeItem(itemConfig.isLogin);
        }
    };

    const handleBack = async () => {
        await handleLogout();
        history.push('/SignIn');
    };

    useEffect(() => {
        if (isEmpty(user)) return;
        const tmpRole = user && user.role ? user.role : 'anonymous';
        setRole(tmpRole);
        setIsLoading(false);
    }, [user]);

    return (
        // eslint-disable-next-line no-nested-ternary
        !isLoading ? (
            role === 'anonymous' ? (
                <Unauthorized clickFct={handleBack} />
            ) : (
                <HkbdbUnauthorized clickFct={handleBack} />
            )
        ) : (
            <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                <CircularProgress />
            </div>
        )
    );
};

export default UnauthorizedPage;
