import React from 'react';
import { useLocation } from 'react-router-dom';
import ResetPassword from './components/ResetPassword';

const AuthPage = () => {
    const queryParams = new URLSearchParams(useLocation().search);
    const oobCode = queryParams.get('oobCode');
    const mode = queryParams.get('mode');
    switch (mode) {
        case 'resetPassword':
            return <ResetPassword oobCode={oobCode} />;
        // case "verifyEmail":
        //   return <VerifyEmail oobCode={oobCode} />;
        default:
            return <div />;
    }
};
export default AuthPage;
