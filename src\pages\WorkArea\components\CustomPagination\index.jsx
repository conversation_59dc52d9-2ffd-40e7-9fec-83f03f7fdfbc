import React, { useMemo } from 'react';
import MenuItem from '@mui/material/MenuItem';
import Pagination from '@mui/material/Pagination';
import Select from '@mui/material/Select';
import './customPagination.scss';

const CustomPagination = ({ totalPages, curPage, setCurPage }) => {
    const pageOptions = useMemo(
        () =>
            Array.from({ length: totalPages }, (_, index) => ({
                key: index + 1,
                text: `${index + 1}`,
                value: index + 1
            })),
        [totalPages]
    );

    const handlePageChange = (event, value) => {
        setCurPage(value);
    };

    const handleDropdownChange = event => {
        setCurPage(event.target.value);
    };

    return (
        <div className="paginationWrapper">
            <div className="paginationContainer">
                <Pagination
                    count={totalPages}
                    page={curPage}
                    onChange={handlePageChange}
                    className="pagination"
                />
            </div>
            <div className="pageIndicator">
                <span>目前位於第</span>
                <Select
                    value={curPage}
                    onChange={handleDropdownChange}
                    style={{
                        minWidth: '88px',
                        borderRadius: '8px',
                        height: '36px'
                    }}
                >
                    {pageOptions.map(option => (
                        <MenuItem key={option.key} value={option.value}>
                            {option.text}
                        </MenuItem>
                    ))}
                </Select>
                <span>頁，共 {totalPages} 頁</span>
            </div>
        </div>
    );
};

export default CustomPagination;
