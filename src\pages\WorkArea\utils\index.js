import axios from 'axios';
import { Base64 } from 'js-base64';
import { v4 as uuidv4 } from 'uuid';
import { Api } from '../../../api/Api';
import { getTotalInformation } from '../../../components/chatGpt/utils/dataMapping';
import defaultEmptyData from '../../../components/chatGpt/utils/defaultEmptyData';
import Act from '../../../store/actions';

export const getExtractInput = async (name, graph) => {
    if (!name || !graph) return '';

    try {
        const baseEncodeName = Base64.encode(encodeURI(name));
        const baseEncodeGraph = Base64.encode(encodeURI(graph));

        const response = await axios.get(Api.getOriginalTextApi(baseEncodeName, baseEncodeGraph));
        const { data } = response.data;
        const originalTextItem = data.find(item => item.property === 'originalText');
        const extractInput = originalTextItem ? originalTextItem.label_zh : '';

        return extractInput;
    } catch (error) {
        console.log('無法獲取擷取文章：', error);
        return '';
    }
};

export const getExtractResult = async (extractId, extractAuthor) => {
    if (!extractId || !extractAuthor) return defaultEmptyData;

    try {
        const graph = Base64.encode(encodeURI(extractId));
        let result = await getTotalInformation(extractAuthor, graph);

        result = result.map(item => ({
            ...item,
            sub: item.sub.map(subItem => {
                const newEntry = {};
                Object.entries(subItem).forEach(([key, obj]) => {
                    newEntry[key] = {
                        ...obj,
                        id: obj.id ? obj.id : uuidv4()
                    };
                });
                return newEntry;
            })
        }));

        return result;
    } catch (error) {
        console.log('無法獲取擷取結果：', error);
        alert('無法獲取擷取結果');

        return defaultEmptyData;
    }
};

export const loadNerPilot = async (dispatch, reviewId, author, reviewStatus, reviewDataset) => {
    dispatch({
        type: Act.CHATGPT_IS_LOADING,
        payload: true
    });

    const graph = Base64.encode(encodeURI(reviewId));
    const input = await getExtractInput(author, graph);
    const result = await getExtractResult(reviewId, author);

    dispatch({
        type: Act.CHATGPT_INPUT_TEXT,
        payload: input
    });
    dispatch({
        type: Act.CHATGPT_OUTPUT_DATA,
        payload: result
    });
    dispatch({
        type: Act.CHATGPT_NUMBER,
        payload: reviewId
    });
    dispatch({
        type: Act.CHATGPT_STATUS,
        payload: reviewStatus
    });
    dispatch({
        type: Act.CHATGPT_SELECTED_DATASET,
        payload: reviewDataset
    });
    dispatch({
        type: Act.CHATGPT_IS_SAVE_IN_WORK_AREA,
        payload: true
    });
    dispatch({
        type: Act.CHATGPT_EXTRACT,
        payload: false
    });

    dispatch({
        type: Act.CHATGPT_IS_LOADING,
        payload: false
    });
};

export const findDatasetObj = (dataset, datasets) => datasets.find(el => el.label === dataset || el.id === dataset);
