/* eslint-disable no-param-reassign */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React, { useContext, useEffect, useState, useRef } from 'react';
import { Drawer, Box, Typography, Backdrop, Button as Mu<PERSON><PERSON><PERSON>on } from '@mui/material';
import { SINGLE_BLOCK_TYPES } from './config/Config';
import Button from './drawer/Button';
import MultiBlock from './drawer/MultiBlock';
import SingleBlock from './drawer/SingleBlock';
import { getCoordinates, saveCoordsChange } from './drawer/utils';
import EditIcon from '../../../images/chatgpt/Vector.svg';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';
import defaultEmptyData from '../utils/defaultEmptyData';
import ConfirmModal from './drawer/ConfirmModal';

// Styles
const styles = {
    backdrop: {
        color: '#fff',
        zIndex: theme => theme.zIndex.drawer + 1,
        pointerEvents: 'auto',
        cursor: 'default',
        backgroundColor: 'rgba(66, 66, 66, 0.1)'
    },
    drawer: {
        zIndex: theme => theme.zIndex.drawer + 2
    },
    drawerPaper: {
        width: 480,
        height: '100%',
        backgroundColor: 'background.default',
        position: 'absolute',
        right: 0,
        top: 0,
        cursor: 'default'
    },
    container: {
        height: '100%',
        display: 'flex',
        flexDirection: 'column'
    },
    header: {
        backgroundColor: '#F4F8FB',
        padding: '16px 24px',
        flex: '0 0 auto',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
    },
    title: {
        color: '#336F89',
        fontWeight: '600'
    },
    content: {
        flex: 1,
        overflowY: 'auto',
        padding: '16px',
        gap: '0.5rem',
        display: 'flex',
        flexDirection: 'column'
    },
    footer: {
        backgroundColor: '#F4F8FB',
        padding: '16px 24px',
        flex: '0 0 auto',
        display: 'flex',
        justifyContent: 'flex-end',
        gap: '1rem'
    },
    addButton: {
        backgroundColor: '#336F89',
        color: '#ffffff',
        padding: '8px 16px',
        borderRadius: '8px'
    }
};

const PersistentDrawer = ({ isChatgptExtract, data, title, classType, comments }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { outputData } = state.chatgpt;
    const reviewStatus = state.chatgpt.status;
    const [isOpen, setIsOpen] = useState(false);
    const [initialData, setInitialData] = useState(data);
    const [updatedData, setUpdatedData] = useState(data);
    const [isUpdated, setIsUpdated] = useState(false);
    const [confirmModalOpen, setConfirmModalOpen] = useState(false);
    const [openCoordsModal, setOpenCoordsModal] = useState(false);
    const [allCoordinates, setAllCoordinates] = useState([]);
    const [savingCoords, setSavingCoords] = useState(false);

    const containerRef = useRef(null);

    const resetData = (newData, status = false) => {
        const clonedData = structuredClone(newData);
        setInitialData(clonedData);
        setUpdatedData(clonedData);
        setIsUpdated(status);
    };

    const handleValueChange = (outerIndex, key, newValue) => {
        const newData = [...initialData];
        newData[outerIndex][key] = {
            ...newData[outerIndex][key],
            value: newValue
        };
        setUpdatedData(newData);
        setIsUpdated(true);
    };

    const replaceName = (items, key, oldBestKnownName, newBestKnownName) => {
        if (oldBestKnownName === newBestKnownName) return;

        items.forEach(item => {
            const names = item[key].value.split('\n');
            if (names.includes(oldBestKnownName)) {
                // 如果新名字已存在，移除舊名字
                if (names.includes(newBestKnownName)) {
                    const filteredNames = names.filter(name => name !== oldBestKnownName);
                    item[key].value = filteredNames.join('\n');
                }
                // 如果新名字不存在，替換舊名字
                else {
                    const updatedNames = names.map(name => (name === oldBestKnownName ? newBestKnownName : name));
                    item[key].value = updatedNames.join('\n');
                }
            }
        });
    };

    const handleSave = () => {
        resetData(data);
        setIsOpen(false);

        const newOutputData = structuredClone(outputData);
        const index = newOutputData.findIndex(item => item.classType === classType);
        // 更改基本資料中的常見名稱，同時也須更改人名資料的常見名稱、出版著作、單篇文章的作家以及其他作品內的相關人物
        if (index !== -1) {
            newOutputData[index].sub = updatedData;
            if (index === 0) {
                const oldBestKnownName = outputData[0].sub[0].bestKnownName.value;
                const newBestKnownName = updatedData[0].bestKnownName.value;

                replaceName(newOutputData[1].sub, 'nnBestKnownName__string', oldBestKnownName, newBestKnownName);
                replaceName(newOutputData[4].sub, 'hasAuthor__Person', oldBestKnownName, newBestKnownName);
                replaceName(newOutputData[5].sub, 'hasAuthor__Person', oldBestKnownName, newBestKnownName);
                replaceName(newOutputData[6].sub, 'hasRelatedPerson__Person', oldBestKnownName, newBestKnownName);
            }
            dispatch({ type: Act.CHATGPT_OUTPUT_DATA, payload: newOutputData });
        }
    };

    const handleCancel = () => {
        resetData(data);
        setIsUpdated(false);
        setIsOpen(false);
    };

    const handleDelete = index => {
        const newData = [...updatedData];
        newData.splice(index, 1);
        resetData(newData, true);
    };

    const handleAdd = () => {
        const template = defaultEmptyData.find(item => item.classType === classType)?.sub || [];
        const newData = [...updatedData, ...template];
        resetData(newData, true);

        setTimeout(() => {
            containerRef.current?.scrollTo({
                top: containerRef.current.scrollHeight,
                behavior: 'smooth'
            });
        }, 0);
    };

    const toggleCoordsModal = (key, open) => {
        setOpenCoordsModal(prev => ({
            ...prev,
            [key]: open
        }));
    };

    const handleChangeCoords = async (coords, index, key) => {
        const formatedCoords = coords.filter(coord => coord.new.label.trim());
        const [_, type] = key.split('__');

        const locations = formatedCoords
            .filter(coord => coord.status !== 'delete')
            .map(coord => coord.new.label)
            .join('\n');

        handleValueChange(index, key, locations);
        try {
            setSavingCoords(true);
            await saveCoordsChange(formatedCoords, type);
            setSavingCoords(false);
        } catch (e) {
            setSavingCoords(false);
            alert('修改經緯度失敗');
            console.log('saveCoordsChange Error: ', e);
        }
    };

    const isSingleBlock = SINGLE_BLOCK_TYPES.includes(classType);

    useEffect(() => {
        resetData(data);
    }, [data]);

    useEffect(() => {
        if (!openCoordsModal) return;

        const openingType = Object.entries(openCoordsModal).find(([_, value]) => value);

        if (!openingType) return;

        const locationType = openingType[0].split('__')[1].toLocaleLowerCase();

        const loadCoordinates = async () => {
            const coordinatesData = await getCoordinates(locationType);
            setAllCoordinates(coordinatesData);
        };

        loadCoordinates();
    }, [openCoordsModal]);

    return (
        <Box sx={{ position: 'relative' }}>
            {(isChatgptExtract
                ? reviewStatus === '' || reviewStatus === 'tempSave'
                : reviewStatus === 'tempSave' || reviewStatus === 'reject') && (
                // hover才出現的div(已改為固定顯示)
                // <div className="accordion-button">
                <MuiButton
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}
                    onClick={e => {
                        e.stopPropagation();
                        setIsOpen(true);
                    }}
                >
                    {/* eslint-disable-next-line jsx-a11y/click-events-have-key-events */}
                    <img src={EditIcon} alt="edit" />
                </MuiButton>
            )}
            <Backdrop sx={styles.backdrop} open={isOpen} onClick={e => e.stopPropagation()}>
                <Drawer
                    anchor="right"
                    open={isOpen}
                    variant="persistent"
                    onClose={() => {}}
                    PaperProps={{ sx: styles.drawerPaper }}
                    sx={styles.drawer}
                >
                    <Box sx={styles.container} onClick={e => e.stopPropagation()}>
                        <Box sx={styles.header}>
                            <Typography variant="h6" sx={styles.title}>
                                編輯 {title}
                            </Typography>
                            {!isSingleBlock && (
                                <Button
                                    clickFunction={handleAdd}
                                    buttonText={`新增${title}`}
                                    isConfirmation
                                    isDisabled={false}
                                />
                            )}
                        </Box>
                        <Box sx={styles.content} ref={containerRef}>
                            {isSingleBlock ? (
                                <SingleBlock
                                    classType={classType}
                                    initialData={updatedData}
                                    changeFct={handleValueChange}
                                    openCoordsModal={openCoordsModal}
                                    allCoordinates={allCoordinates}
                                    toggleCoordsModal={toggleCoordsModal}
                                    handleChangeCoords={handleChangeCoords}
                                    savingCoords={savingCoords}
                                    reviewStatus={reviewStatus}
                                    comments={comments}
                                />
                            ) : (
                                <MultiBlock
                                    classType={classType}
                                    initialData={updatedData}
                                    changeFct={handleValueChange}
                                    title={title}
                                    deleteFct={handleDelete}
                                    openCoordsModal={openCoordsModal}
                                    allCoordinates={allCoordinates}
                                    toggleCoordsModal={toggleCoordsModal}
                                    handleChangeCoords={handleChangeCoords}
                                    savingCoords={savingCoords}
                                    reviewStatus={reviewStatus}
                                    comments={comments}
                                />
                            )}
                        </Box>
                        <Box sx={styles.footer}>
                            <Button
                                clickFunction={e => {
                                    e.stopPropagation();
                                    // eslint-disable-next-line no-unused-expressions
                                    isUpdated ? setConfirmModalOpen(true) : setIsOpen(false);
                                }}
                                isDisabled={false}
                                buttonText="取消"
                                isConfirmation={false}
                            />
                            <Button
                                clickFunction={e => {
                                    e.stopPropagation();
                                    handleSave();
                                }}
                                isDisabled={!isUpdated}
                                buttonText="儲存"
                                isConfirmation
                            />
                        </Box>
                    </Box>
                </Drawer>
                <ConfirmModal open={confirmModalOpen} setOpen={setConfirmModalOpen} confirmFct={handleCancel} />
            </Backdrop>
        </Box>
    );
};

export default PersistentDrawer;
