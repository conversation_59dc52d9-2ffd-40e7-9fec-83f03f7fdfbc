import React, { useCallback, useEffect, useRef, useState } from 'react';
import AddIcon from '@mui/icons-material/Add';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Chip from '@mui/material/Chip';
import CircularProgress from '@mui/material/CircularProgress';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import CustomTable from './components/CustomTable';
import Map from './components/Map';
import { defaultStyles, DEFAULT_COORDINATES, api } from './constants';
import InfoIcon from './icon/InfoIcon';
import defMapPinIcon from './icon/map-pin.svg';

const CoordinatesModal = ({
    open = false,
    setOpen = () => {},
    readOnly = false,
    isLoading = false,
    locations = [],
    title = '',
    tableInfo = '可在下方欄位新增多筆資料，並於地圖上標示對應座標，方便快速確認是否正確同步更新至資料庫。',
    tableHeader = ['名稱', '緯度', '經度'],
    mapInfo = '地圖將顯示包含完整經緯度的資料。',
    coordinateOptions = [],
    googleMapsGeoApiKey = '',
    mapCenter = [22.28056, 114.17222],
    mapZoom = 7,
    getCoordinates = null,
    onClick = () => {},
    infoIcon = null,
    addCoordinatesIcon = null,
    mapPinIcon = null,
    deleteIcon = null,
    sx = {},
    chipSx = {},
    triggerSx = {},
    modalSx = {},
    titleSx = {},
    contentSx = {},
    panelSx = {},
    cancelBtnSx = {},
    saveBtnSx = {},
    closeBtnSx = {},
    addCoordinateBtnSx = {},
    tableSx = {},
    tableInfoSx = {},
    tableHeadSx = {},
    tableHeadCellSx = {},
    tableBodySx = {},
    tableBodyCellSx = {},
    tableBodyInputSx = {},
    tableWarnInfo = {},
    mapInfoSx = {},
    mapInfoIconSx = {},
    deleteButtonSx = {},
    deleteIconSx = {},
    noDataSx = {},
    editCoordinatesContainerSx = {},
    mapSx = {}
}) => {
    const [editCoords, setEditCoords] = useState([]);
    const [coordOptions, setCoordOptions] = useState([]);
    const debounceTimer = useRef(null);
    const requestIdRef = useRef(null);

    const missingCoord = editCoords.find(coord => !coord.new.label && (coord.new.longitude || coord.new.latitude));

    const handleClose = () => {
        setOpen(false);
        setEditCoords([]);
        setCoordOptions([]);
    };

    const getGoogleCoords = useCallback(async (label, apiKey) => {
        if (!label || !apiKey) return null;

        try {
            const res = await axios.get(api.googleMapsGeoApi(label, apiKey));

            if (res.data.status !== 'OK' || !res.data.results.length) return null;

            return res.data.results[0].geometry.location;
        } catch (e) {
            console.log('getGoogleCoords failed: ', e);
            return null;
        }
    }, []);

    const transformEditCoords = dt =>
        dt.map(item => {
            if (item.id && item.old && item.new && item.status) {
                return item;
            }

            return {
                id: uuidv4(),
                old: item,
                new: item
            };
        });

    const transformCoordOptions = dt =>
        dt.map(item => {
            const longitude = String(Number(item.longitude) || '');
            const latitude = String(Number(item.latitude) || '');

            return {
                ...item,
                id: item.id || uuidv4(),
                longitude,
                latitude
            };
        });

    const readCoord = useCallback(
        async value => {
            if (!value) return DEFAULT_COORDINATES;

            const foundOwnedOptions = coordOptions.find(({ label }) => label === value);
            if (foundOwnedOptions) return foundOwnedOptions;

            if (typeof getCoordinates === 'function') {
                const res = await getCoordinates(value);
                if (res && res.longitude && res.latitude) {
                    return { ...res, id: '' };
                }
            }

            if (googleMapsGeoApiKey) {
                const res = await getGoogleCoords(value, googleMapsGeoApiKey);
                if (res) {
                    return { id: '', label: value, latitude: res.lat, longitude: res.lng };
                }
            }

            return { ...DEFAULT_COORDINATES, label: value };
        },
        [coordOptions, getCoordinates, googleMapsGeoApiKey, getGoogleCoords]
    );

    const handleChange = (id, type, value) => {
        const currentRequestId = uuidv4();
        requestIdRef.current = currentRequestId;

        setEditCoords(prevCoords =>
            prevCoords.map(item => {
                if (item.id !== id) return item;

                const updatedNew = { ...item.new, [type]: value };

                if (type === 'label') {
                    if (debounceTimer.current) clearTimeout(debounceTimer.current);

                    debounceTimer.current = setTimeout(() => {
                        readCoord(value).then(foundCoord => {
                            if (currentRequestId !== requestIdRef.current) return;

                            setEditCoords(prev =>
                                prev.map(coord => {
                                    if (coord.id !== id) return coord;

                                    if (foundCoord)
                                        return {
                                            ...coord,
                                            new: foundCoord,
                                            old: foundCoord
                                        };

                                    return {
                                        ...coord,
                                        new: { ...updatedNew, id: '', latitude: '', longitude: '' },
                                        old: DEFAULT_COORDINATES
                                    };
                                })
                            );
                        });
                    }, 1000);

                    return { ...item, new: updatedNew };
                }

                const foundOption = coordOptions.find(({ label }) => item?.new?.label === label);

                if (foundOption) return { ...item, new: updatedNew };

                return { ...item, new: { ...updatedNew, id: '' }, old: DEFAULT_COORDINATES };
            })
        );
    };

    const handleAddCoordinates = () => {
        setEditCoords(prev => [
            ...prev,
            {
                id: uuidv4(),
                old: DEFAULT_COORDINATES,
                new: DEFAULT_COORDINATES
            }
        ]);
    };

    const handleDeleteCoordinates = id => {
        setEditCoords(prev => {
            const foundEditCoords = prev.find(coord => coord.id === id);
            const inCoordOptions = coordOptions.some(coord => coord.label === foundEditCoords.new.label);

            if (!foundEditCoords) return prev;

            if (!foundEditCoords.new.label || !inCoordOptions) {
                return prev.filter(item => item.id !== id);
            }

            return prev.map(item => (item.id === id ? { ...item, status: 'delete' } : item));
        });
    };

    useEffect(() => {
        const currentRequestId = uuidv4();
        requestIdRef.current = currentRequestId;

        const loadCoords = async () => {
            if (!locations?.length || !open) return;

            const editCoordinates = await Promise.all(
                locations.map(async location => {
                    const foundCoord = await readCoord(location);
                    return foundCoord || { ...DEFAULT_COORDINATES, label: location };
                })
            );

            if (currentRequestId !== requestIdRef.current) return;

            const transformData = transformEditCoords(editCoordinates);
            setEditCoords(transformData);
        };

        loadCoords();
    }, [readCoord, locations, open]);

    useEffect(() => {
        if (coordinateOptions?.length === 0) return;

        const transformData = transformCoordOptions(coordinateOptions);

        setCoordOptions(transformData);
    }, [coordinateOptions]);

    useEffect(() => {
        if (!editCoords?.length) return;

        const updatedData = editCoords.map(el => {
            if (el.status === 'delete') return el;

            const existsInOptions = el.new && coordOptions.some(option => option.label === el.new.label);

            if (existsInOptions) {
                return JSON.stringify(el.new) === JSON.stringify(el.old)
                    ? { ...el, status: 'default' }
                    : { ...el, status: 'update' };
            }

            return { ...el, status: 'create' };
        });

        if (JSON.stringify(updatedData) === JSON.stringify(editCoords)) return;

        setEditCoords(updatedData);
    }, [coordOptions, editCoords]);

    return (
        <Box sx={{ ...defaultStyles.sx, ...sx }}>
            <Button onClick={() => setOpen(true)} sx={{ ...defaultStyles.triggerSx, ...triggerSx }}>
                {locations?.length > 0 &&
                    locations.map(location => (
                        <Chip key={location} label={location} sx={{ ...defaultStyles.chipSx, ...chipSx }} />
                    ))}
            </Button>
            <Modal open={open} onClose={handleClose}>
                <Box sx={{ ...defaultStyles.modalSx, ...modalSx }}>
                    {isLoading ? (
                        <Box
                            sx={{
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                                gap: '16px'
                            }}
                        >
                            <CircularProgress />
                            <Typography>loading...</Typography>
                        </Box>
                    ) : (
                        <>
                            <Typography sx={{ ...defaultStyles.titleSx, ...titleSx }}>編輯 {title} 經緯度</Typography>
                            <Box sx={{ ...defaultStyles.contentSx, ...contentSx }}>
                                <Box
                                    sx={{ ...defaultStyles.editCoordinatesContainerSx, ...editCoordinatesContainerSx }}
                                >
                                    {!readOnly && (
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                justifyContent: 'space-between',
                                                marginBottom: '8px'
                                            }}
                                        >
                                            {tableInfo && (
                                                <Typography sx={{ ...defaultStyles.tableInfoSx, ...tableInfoSx }}>
                                                    {tableInfo}
                                                </Typography>
                                            )}
                                            <Button
                                                sx={{ ...defaultStyles.addCoordinateBtnSx, ...addCoordinateBtnSx }}
                                                onClick={handleAddCoordinates}
                                            >
                                                {addCoordinatesIcon !== false &&
                                                    (addCoordinatesIcon || (
                                                        <AddIcon sx={{ width: '16px', height: '16px' }} />
                                                    ))}
                                                新增經緯度
                                            </Button>
                                        </Box>
                                    )}

                                    <CustomTable
                                        readOnly={readOnly}
                                        tableHeader={tableHeader}
                                        editCoords={editCoords.filter(location => location.status !== 'delete')}
                                        coordOptions={coordOptions}
                                        handleChange={handleChange}
                                        handleDeleteCoordinates={handleDeleteCoordinates}
                                        deleteIcon={deleteIcon}
                                        tableSx={{ ...defaultStyles.tableSx, ...tableSx }}
                                        tableHeadSx={{ ...defaultStyles.tableHeadSx, ...tableHeadSx }}
                                        tableHeadCellSx={{ ...defaultStyles.tableHeadCellSx, ...tableHeadCellSx }}
                                        tableBodySx={{ ...defaultStyles.tableBodySx, ...tableBodySx }}
                                        tableBodyCellSx={{ ...defaultStyles.tableBodyCellSx, ...tableBodyCellSx }}
                                        tableBodyInputSx={{ ...defaultStyles.tableBodyInputSx, ...tableBodyInputSx }}
                                        deleteButtonSx={{ ...defaultStyles.deleteButtonSx, ...deleteButtonSx }}
                                        deleteIconSx={{ ...defaultStyles.deleteIconSx, ...deleteIconSx }}
                                        noDataSx={{ ...defaultStyles.noDataSx, ...noDataSx }}
                                    />

                                    {missingCoord && (
                                        <Typography sx={{ ...defaultStyles.tableWarnInfo, ...tableWarnInfo }}>
                                            請填寫必填欄位。
                                        </Typography>
                                    )}
                                </Box>

                                {mapInfo && (
                                    <Box sx={{ ...defaultStyles.mapInfoSx, ...mapInfoSx }}>
                                        {infoIcon !== false &&
                                            (infoIcon || (
                                                <InfoIcon sx={{ ...defaultStyles.mapInfoIconSx, ...mapInfoIconSx }} />
                                            ))}
                                        {mapInfo}
                                    </Box>
                                )}

                                <Map
                                    editCoords={editCoords.filter(({ status }) => status !== 'delete')}
                                    mapPinIcon={mapPinIcon || defMapPinIcon}
                                    defaultCenter={mapCenter}
                                    defaultZoom={mapZoom}
                                    sx={{ ...defaultStyles.mapSx, ...mapSx }}
                                />
                            </Box>
                            <Box sx={{ ...defaultStyles.panelSx, ...panelSx }}>
                                {readOnly && (
                                    <Button sx={{ ...defaultStyles.closeBtnSx, ...closeBtnSx }} onClick={handleClose}>
                                        關閉
                                    </Button>
                                )}
                                {!readOnly && (
                                    <Button sx={{ ...defaultStyles.cancelBtnSx, ...cancelBtnSx }} onClick={handleClose}>
                                        取消
                                    </Button>
                                )}
                                {!readOnly && (
                                    <Button
                                        sx={{ ...defaultStyles.saveBtnSx, ...saveBtnSx }}
                                        onClick={() => {
                                            if (missingCoord) return;

                                            const filteredEditCoords = editCoords.filter(coord => coord.new.label);

                                            onClick(filteredEditCoords);

                                            handleClose();
                                        }}
                                    >
                                        確定
                                    </Button>
                                )}
                            </Box>
                        </>
                    )}
                </Box>
            </Modal>
        </Box>
    );
};

export default CoordinatesModal;
