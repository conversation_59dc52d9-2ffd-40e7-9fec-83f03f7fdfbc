import React, { useContext, useState } from 'react';
import '../SignUpPage.scss';
import { zodResolver } from '@hookform/resolvers/zod';
import { CircularProgress, Backdrop } from '@mui/material';
import { getAuth, sendEmailVerification, updateProfile, createUserWithEmailAndPassword, signOut } from 'firebase/auth';
import { produce } from 'immer';
import { useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';

import * as z from 'zod';
import { updateUser } from '../../../api/firebase/realtimeDatabase';
import { getFormatUser, isEmpty } from '../../../common/codes';
import { StoreContext } from '../../../store/StoreProvider';

// 定義驗證模式
const signUpSchema = z.object({
    displayName: z.string().refine(val => val !== '', '欄位必填'),
    institution: z.string().refine(val => val !== '', '欄位必填'),
    position: z.string().refine(val => val !== '', '欄位必填'),
    researchPurpose: z.string().refine(val => val !== '', '欄位必填')
});

const SignUpDetail = () => {
    const {
        register,
        handleSubmit,
        watch,
        formState: { errors }
    } = useForm({
        resolver: zodResolver(signUpSchema)
    });
    const watchAllFields = watch();

    const [state, _] = useContext(StoreContext);
    const { registerUserInfo } = state.signUp;
    const { user } = state;
    const [errorMsg, setErrorMsg] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const history = useHistory();
    const auth = getAuth();
    const handleSignUp = async e => {
        const { email, displayName, password, confirmPassword, institution, position, researchPurpose } = e;
        if (isEmpty(email)) return;
        if (password !== confirmPassword) {
            return;
        }

        const signUp = async () => {
            let tmpUser = null;
            try {
                // 第一步：創建
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);

                tmpUser = userCredential.user;

                // 第二步：更新使用者資料
                await updateProfile(tmpUser, {
                    displayName
                });

                // 第三步：發送驗證信
                await sendEmailVerification(tmpUser);

                // 第四步：準備更新使用者額外資訊
                const updateObj = {
                    institution,
                    position,
                    researchPurpose
                };

                const userInfo = getFormatUser(tmpUser);
                const { uid } = userInfo;
                const filteredUserInfo = Object.fromEntries(
                    Object.entries(userInfo).filter(([, value]) => value !== undefined)
                );

                // 第五步：更新使用者資料到firebase
                if (uid && (displayName || email)) {
                    await updateUser(uid, {
                        role: 'anonymous',
                        ...filteredUserInfo,
                        ...updateObj
                    });
                }
                return true; // 明確返回成功狀態
                // eslint-disable-next-line no-shadow
            } catch (error) {
                const ec = error.code;
                if (ec === 'auth/email-already-in-use') {
                    setErrorMsg('此電子郵件已被註冊');
                } else if (ec === 'auth/invalid-email') {
                    setErrorMsg('電子郵件格式不正確');
                }
                throw error; // 確保錯誤被正確傳遞
            }
        };

        try {
            // 等待註冊完成
            await signUp();

            // 確保註冊完成後才登出
            await signOut(auth);

            // eslint-disable-next-line consistent-return
            return true;
            // eslint-disable-next-line no-shadow
        } catch (error) {
            console.error('error.message', error.message);
            // eslint-disable-next-line consistent-return
            return false;
        }
    };

    const handleSignUpForGoogle = async e => {
        const { displayName, institution, position, researchPurpose } = e;
        const signUp = async () => {
            const tmpUser = auth.currentUser;
            try {
                // 第二步：更新使用者資料
                await updateProfile(tmpUser, {
                    displayName
                });

                const updateObj = {
                    institution,
                    position,
                    researchPurpose
                };
                const userInfo = getFormatUser(tmpUser);
                const { uid } = userInfo;
                // 第五步：更新使用者資料到firebase
                if (!isEmpty(updateObj)) {
                    await updateUser(uid, {
                        role: 'anonymous',
                        ...updateObj
                    });
                }
                return true; // 明確返回成功狀態
                // eslint-disable-next-line no-shadow
            } catch (error) {
                console.error('Sign-up error:', error);
                throw error; // 確保錯誤被正確傳遞
            }
        };

        try {
            // 等待註冊完成
            await signUp();

            // 確保註冊完成後才登出
            // await firebase.auth().signOut();
            await signOut(auth);
            return true;
            // eslint-disable-next-line no-shadow
        } catch (error) {
            console.error('註冊過程發生錯誤:', error);
            return false;
        }
    };

    const onSubmit = async data => {
        setIsLoading(true);
        const userGoogleFullData = produce(user, draft => {
            // merge data into draft
            Object.assign(draft, data);
        });
        const userFullData = produce(registerUserInfo, draft => {
            // merge data into draft
            Object.assign(draft, data);
        });
        let res;
        if (user?.providerId === 'google.com') {
            res = await handleSignUpForGoogle(userGoogleFullData);
        } else {
            res = await handleSignUp(userFullData);
        }
        if (res) {
            history.push('/SignUp/success');
        }
        setIsLoading(false);
    };

    const handleBack = async () => {
        if (user?.providerId === 'google.com') {
            history.push('/SignOut');
            return;
        }
        history.goBack();
    };

    const isFormFilled = () => {
        const requiredFields = ['displayName', 'institution', 'position', 'researchPurpose'];
        return requiredFields.every(
            field => watchAllFields[field]?.trim() !== '' && watchAllFields[field] !== undefined
        );
    };

    return (
        <div className="signUpPage">
            <div className="signUp">
                <form onSubmit={handleSubmit(onSubmit)} className="signUpForm">
                    <h1 className="signUpTitle">註冊帳戶</h1>
                    <div className="signUpComment">
                        <p>現時香港大專院校人士以院校電郵可作自動註冊，其他研究人員註冊將由管理員手動審批。</p>
                    </div>
                    <div className="signUpField">
                        <div className="signUpField-titleBox">
                            <span className="signUpField-titleBox-title">
                                姓名
                                <span>*</span>
                            </span>
                            {errors.displayName && (
                                <span className="signUpField-titleBox-error">*{errors.displayName.message}</span>
                            )}
                        </div>
                        <input {...register('displayName')} type="displayName" />
                        <p className="signUpField-memo">中/英文皆可</p>
                    </div>
                    <div className="signUpField">
                        <div className="signUpField-titleBox">
                            <span className="signUpField-titleBox-title">
                                院校
                                <span>*</span>
                            </span>
                            {errors.institution && (
                                <span className="signUpField-titleBox-error">*{errors.institution.message}</span>
                            )}
                        </div>
                        <input {...register('institution')} type="institution" />
                    </div>
                    <div className="signUpField">
                        <div className="signUpField-titleBox">
                            <span className="signUpField-titleBox-title">
                                職位
                                <span>*</span>
                            </span>
                            {errors.position && (
                                <span className="signUpField-titleBox-error">*{errors.position.message}</span>
                            )}
                        </div>
                        <input {...register('position')} type="position" />
                    </div>
                    <div className="signUpField">
                        <div className="signUpField-titleBox">
                            <span className="signUpField-titleBox-title">
                                研究目的
                                <span>*</span>
                            </span>
                            {errors.researchPurpose && (
                                <span className="signUpField-titleBox-error">*{errors.researchPurpose.message}</span>
                            )}
                        </div>
                        <input {...register('researchPurpose')} type="researchPurpose" />
                    </div>
                    <div className="signUpBtnArea">
                        <button type="button" className="signUpBtnArea-back" onClick={handleBack}>
                            返回
                        </button>
                        <button
                            type="submit"
                            className={`signUpBtnArea-forward ${!isFormFilled() ? 'signUpBtnArea-forward-disabled' : ''}`}
                            disabled={!isFormFilled()}
                        >
                            下一步
                        </button>
                    </div>
                </form>
                {errorMsg && (
                    <div style={{ textAlign: 'center', color: 'red', marginTop: '1rem' }}>
                        <p>注意：{errorMsg}</p>
                    </div>
                )}
                {isLoading && (
                    <Backdrop open={isLoading}>
                        <div style={{ display: 'flex', justifyContent: 'center' }}>
                            <CircularProgress />
                        </div>
                    </Backdrop>
                )}
            </div>
        </div>
    );
};

export default SignUpDetail;
