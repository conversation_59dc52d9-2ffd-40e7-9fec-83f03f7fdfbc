import React, { useContext } from 'react';
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { Base64 } from 'js-base64';
import { Link } from 'react-router-dom';
import StatusIcon from './StatusIcon';
import { deleteGraph } from '../../../components/chatGpt/utils/entry';
import disabledDeleteIcon from '../../../images/disabled-delete-icon.svg';
import { StoreContext } from '../../../store/StoreProvider';
import { statusIconMap } from '../constants';
import { findDatasetObj, loadNerPilot } from '../utils';
import CustomCheckModal from './CustomCheckModal';
import deleteIcon from '../../../images/delete-icon.svg';

const CustomTable = ({ headers, data, loadReviewData }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { chatgptDataset } = state.chatgpt;

    const statusIconMapping = statusType => {
        const statusDetail = statusIconMap.find(el => el.id === statusType);

        return (
            <StatusIcon text={statusDetail.text} bgColor={statusDetail.bgcolor} textColor={statusDetail.textColor} />
        );
    };

    const handleClickDelete = async graph => {
        const encodedGraph = Base64.encode(encodeURI(graph));

        await deleteGraph(encodedGraph);
        await loadReviewData();
    };

    return (
        <Box
            sx={{
                border: '1px solid #E9F1F5',
                borderRadius: '12px',
                overflow: 'hidden'
            }}
        >
            <Table
                sx={{
                    '& .MuiTableCell-root': {
                        borderRight: '1px solid #E9F1F5',
                        borderBottom: 'none'
                    },
                    '& .MuiTableRow-root:not(:last-of-type)': {
                        borderBottom: '1px solid #E9F1F5'
                    },
                    '& .MuiTableRow-root:last-of-type .MuiTableCell-root': {
                        borderBottom: 'none'
                    }
                }}
            >
                <TableHead>
                    <TableRow sx={{ backgroundColor: '#F4F8FB' }}>
                        {headers.map(header => (
                            <TableCell
                                key={header}
                                sx={{
                                    color: '#336F89',
                                    fontWeight: 600,
                                    borderRight: '1px solid #E9F1F5'
                                }}
                            >
                                {header}
                            </TableCell>
                        ))}
                    </TableRow>
                </TableHead>
                <TableBody>
                    {data.map(el => (
                        <TableRow key={el.nerID}>
                            <TableCell component="th" scope="row">
                                {el.nerTime}
                            </TableCell>
                            <TableCell align="left">
                                <Link
                                    to="/"
                                    style={{ color: '#336F89' }}
                                    onClick={() => {
                                        loadNerPilot(
                                            dispatch,
                                            el.nerID,
                                            el.nerPerson,
                                            el.nerStatus,
                                            findDatasetObj(el.nerDataset, chatgptDataset)
                                        );
                                    }}
                                >
                                    {el.nerID}
                                </Link>
                            </TableCell>
                            <TableCell align="left">{el.nerDataset}</TableCell>
                            <TableCell align="left">{el.nerPerson}</TableCell>
                            <TableCell align="left">{statusIconMapping(el.nerStatus)}</TableCell>
                            <TableCell align="left">{el.nerDesc || ''}</TableCell>
                            <TableCell align="left">
                                <CustomCheckModal
                                    trigger={
                                        <img
                                            src={el.nerStatus !== 'tempSave' ? disabledDeleteIcon : deleteIcon}
                                            alt="delete"
                                        />
                                    }
                                    title="刪除資料"
                                    description="是否確定刪除該筆資料？"
                                    actionBtnText="刪除"
                                    actionBtnStyle={{
                                        bgcolor: '#D14835',
                                        color: '#FFFFFF',
                                        '&:hover': {
                                            bgcolor: '#c13e2c'
                                        }
                                    }}
                                    disabled={el.nerStatus !== 'tempSave'}
                                    onClick={() => {
                                        handleClickDelete(el.nerID);
                                    }}
                                />
                            </TableCell>
                        </TableRow>
                    ))}
                </TableBody>
            </Table>
        </Box>
    );
};

export default CustomTable;
