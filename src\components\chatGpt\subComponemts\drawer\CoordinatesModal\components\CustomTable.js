import React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Input from '@mui/material/Input';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import CustomAutocomplete from './CustomAutocomplete';
import DeleteIcon from '../icon/DeleteIcon';

const CustomTable = ({
    readOnly,
    tableHeader,
    editCoords,
    coordOptions,
    handleChange,
    handleDeleteCoordinates,
    deleteIcon,
    tableSx,
    tableHeadSx,
    tableHeadCellSx,
    tableBodySx,
    tableBodyCellSx,
    tableBodyInputSx,
    deleteButtonSx,
    deleteIconSx,
    noDataSx
}) => (
    <Box sx={tableSx} inert={readOnly ? 'true' : undefined}>
        <TableContainer component={Paper} sx={tableHeadSx}>
            <Table>
                <colgroup>
                    <col style={{ width: '7%' }} />
                    {tableHeader.map(header => (
                        <col key={header} style={{ width: `${(readOnly ? 93 : 85) / tableHeader.length}%` }} />
                    ))}
                    {!readOnly && <col style={{ width: '8%' }} />}
                </colgroup>
                <TableHead>
                    <TableRow>
                        <TableCell sx={tableHeadCellSx} />
                        {tableHeader.map(header => (
                            <TableCell key={header} align="left" sx={tableHeadCellSx}>
                                {header}
                            </TableCell>
                        ))}
                        {!readOnly && (
                            <TableCell align="left" sx={tableHeadCellSx}>
                                刪除
                            </TableCell>
                        )}
                    </TableRow>
                </TableHead>
            </Table>
        </TableContainer>

        <TableContainer component={Paper} sx={tableBodySx}>
            <Table>
                <colgroup>
                    <col style={{ width: '7%' }} />
                    {tableHeader.map(header => (
                        <col key={header} style={{ width: `${(readOnly ? 93 : 85) / tableHeader.length}%` }} />
                    ))}
                    {!readOnly && <col style={{ width: '8%' }} />}
                </colgroup>
                <TableBody>
                    {editCoords?.length > 0 ? (
                        editCoords.map((coord, index) => (
                            <TableRow key={coord.id}>
                                <TableCell align="left" sx={tableBodyCellSx}>
                                    {index + 1}
                                </TableCell>
                                <TableCell align="left" sx={tableBodyCellSx}>
                                    <CustomAutocomplete
                                        value={coord.new.label}
                                        options={coordOptions}
                                        onChange={newValue => handleChange(coord.id, 'label', newValue)}
                                        sx={{
                                            '& input': {
                                                ...tableBodyInputSx,
                                                border:
                                                    !coord.new.label && (coord.new.longitude || coord.new.latitude)
                                                        ? '1px solid #D14835'
                                                        : tableBodyInputSx.border,
                                                '&:focus': {
                                                    border:
                                                        !coord.new.label && (coord.new.longitude || coord.new.latitude)
                                                            ? '1px solid #D14835'
                                                            : tableBodyInputSx['&:focus'].border
                                                }
                                            }
                                        }}
                                    />
                                </TableCell>
                                <TableCell align="left" sx={tableBodyCellSx}>
                                    <Input
                                        value={coord.new.latitude}
                                        disableUnderline
                                        sx={{ width: '100%', '& input': tableBodyInputSx }}
                                        onChange={e => {
                                            if (/^(-?[0-9]*\.?[0-9]*)?$/.test(e.target.value))
                                                handleChange(coord.id, 'latitude', e.target.value);
                                        }}
                                    />
                                </TableCell>
                                <TableCell align="left" sx={tableBodyCellSx}>
                                    <Input
                                        value={coord.new.longitude}
                                        disableUnderline
                                        sx={{ width: '100%', '& input': tableBodyInputSx }}
                                        onChange={e => {
                                            if (/^(-?[0-9]*\.?[0-9]*)?$/.test(e.target.value))
                                                handleChange(coord.id, 'longitude', e.target.value);
                                        }}
                                    />
                                </TableCell>
                                {!readOnly && (
                                    <TableCell align="left" sx={tableBodyCellSx}>
                                        <Button sx={deleteButtonSx} onClick={() => handleDeleteCoordinates(coord.id)}>
                                            {deleteIcon || <DeleteIcon sx={deleteIconSx} />}
                                        </Button>
                                    </TableCell>
                                )}
                            </TableRow>
                        ))
                    ) : (
                        <TableRow>
                            <TableCell colSpan={5} sx={noDataSx}>
                                暫無資料
                            </TableCell>
                        </TableRow>
                    )}
                </TableBody>
            </Table>
        </TableContainer>
    </Box>
);

export default CustomTable;
