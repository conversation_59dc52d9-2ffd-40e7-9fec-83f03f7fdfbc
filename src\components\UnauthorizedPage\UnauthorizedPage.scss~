.unauthorized{
  p,
  h1 {
    margin: 0;
  }
  height: 100vh;
  width: 100%;
  padding-top: 8rem;
  background: linear-gradient(180deg, #f9fafc 0%, #f4f8fb 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  &-container{
    //display: flex;
    //flex-direction: column;
    //justify-content: center;
    //align-items: center;
    //width: 100%;
    min-width: 480px;
    &-box{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      &-content{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 1rem;
        width: 100%;
      }
      h1{
        color: #336F89;
        font-size: 24px;
        font-weight: 600;
      }
      p{
        font-size: 14px;
      }
    }
  }
  .unauthorizedBtnArea{
      display: flex;
      gap: 0.5rem;
      width: 100%;
      &-back{
        background-color: transparent;
        color: #336F89;
        cursor: pointer;
      }
      button{
        width: 100%;
        border: 1px solid #DAE9EF;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
      }
  }
}