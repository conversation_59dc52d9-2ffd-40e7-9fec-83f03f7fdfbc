export const defaultStyles = {
    sx: {},
    chipSx: { margin: '2px', color: '#336F89', backgroundColor: '#F4F8FB' },
    triggerSx: {
        width: '100%',
        display: 'flex',
        justifyContent: 'start',
        border: '1px solid #EEEEEE',
        borderRadius: '8px',
        flexWrap: 'wrap',
        minHeight: '38px',
        '&:hover': { backgroundColor: 'transparent' }
    },
    modalSx: {
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: '90%',
        height: '85%',
        backgroundColor: '#FFFFFF',
        boxShadow: '0px 2px 8px 0px #0000000F',
        borderRadius: '12px',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between'
    },
    titleSx: {
        padding: '16px 24px',
        borderRadius: '12px 12px 0 0',
        fontSize: '20px',
        fontWeight: 600,
        backgroundColor: '#F4F8FB',
        color: '#336F89',
        borderBottom: '1px solid #DAE9EF'
    },
    contentSx: {
        padding: '24px 24px 24px 24px',
        flexGrow: 1,
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '24px'
    },
    panelSx: { padding: '24px', display: 'flex', gap: '24px', justifyContent: 'end', borderTop: '1px solid #E9F1F5' },
    cancelBtnSx: {
        padding: '8px 16px',
        color: '#757575',
        backgroundColor: '#FAFAFA',
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px'
    },
    saveBtnSx: {
        padding: '8px 16px',
        color: '#FFFFFF',
        backgroundColor: '#336F89',
        '&:hover': { backgroundColor: '#336F89' },
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px'
    },
    closeBtnSx: {
        padding: '8px 16px',
        color: '#FFFFFF',
        backgroundColor: '#336F89',
        '&:hover': { backgroundColor: '#336F89' },
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px'
    },
    addCoordinateBtnSx: {
        padding: '8px 16px 8px 12px',
        display: 'flex',
        gap: '8px',
        alignItems: 'center',
        backgroundColor: '#F4F8FB',
        color: '#336F89',
        borderRadius: '8px',
        fontWeight: 600,
        fontSize: '14px'
    },
    tableSx: { display: 'flex', flexDirection: 'column', gap: '8px' },
    tableInfoSx: { fontSize: '14px' },
    tableHeadSx: { backgroundColor: '#F4F8FB', border: '1px solid #E9F1F5', boxShadow: 'none', borderRadius: '8px' },
    tableHeadCellSx: {
        color: '#336F89',
        fontWeight: 600,
        fontSize: '14px',
        borderRight: '1px solid #E9F1F5',
        borderBottom: 'none'
    },
    tableBodySx: { border: '1px solid #E9F1F5', borderRadius: '8px', boxShadow: 'none' },
    tableBodyCellSx: { borderRight: '1px solid #E9F1F5', borderBottom: '1px solid #E9F1F5' },
    tableBodyInputSx: {
        border: '1px solid #EEEEEE',
        borderRadius: '8px',
        padding: '8px 12px 8px 16px !important',
        '&:focus': { border: '1px solid #336F89' }
    },
    tableWarnInfo: { color: '#D14835', fontSize: '14px', fontWeight: 400 },
    mapInfoSx: {
        display: 'flex',
        gap: '4px',
        padding: '12px 16px ',
        fontSize: '14px',
        backgroundColor: '#F4F8FB',
        color: '#336F89',
        borderRadius: '8px',
        alignItems: 'center'
    },
    mapInfoIconSx: { width: '16px', height: '16px', color: '#336F89' },
    deleteButtonSx: { padding: '8px', backgroundColor: 'transparent', minWidth: '0px' },
    deleteIconSx: { width: '16px', height: '16px', color: '#336F89' },
    noDataSx: { textAlign: 'center', padding: '20px', color: '#757575' },
    editCoordinatesContainerSx: { display: 'flex', flexDirection: 'column', gap: '8px' },
    mapSx: { minHeight: '500px', width: '100%', borderRadius: '8px' }
};

export const DEFAULT_COORDINATES = { id: '', label: '', longitude: '', latitude: '' };

export const api = {
    googleMapsGeoApi: (address, key) =>
        `https://maps.googleapis.com/maps/api/geocode/json?address=${address}&key=${key}`
};
