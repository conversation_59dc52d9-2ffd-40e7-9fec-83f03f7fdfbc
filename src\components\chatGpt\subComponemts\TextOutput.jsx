import React, { useContext, useEffect, useState } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { Box } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import Typography from '@mui/material/Typography';
import Grid from '@mui/system/Unstable_Grid';
import '../Chatgpt.scss';
import axios from 'axios';
import { Base64 } from 'js-base64';
import CommentModal from './CommentModal';
import { SINGLE_BLOCK_TYPES } from './config/Config';
import PersistentDrawer from './PersistentDrawer';
import { Api } from '../../../api/Api';
import { StoreContext } from '../../../store/StoreProvider';
import { commentsMapping } from '../utils';

const DetailComponent = ({ detailData, checked, title, classType, comments, status }) => {
    const isSingleBlock = SINGLE_BLOCK_TYPES.includes(classType);

    const singleBlock = (data, key, i) => {
        const close = data.value.length === 0 ? 'close' : '';

        return (
            <AccordionDetails
                // eslint-disable-next-line react/no-array-index-key
                key={key}
                className={`dataDetail ${!checked ? close : ''}`}
                sx={{
                    '&.dataDetail': {
                        padding: '0px 40px 8px 40px',
                        '&.close': {
                            display: 'none'
                        }
                    }
                }}
            >
                <Grid
                    container
                    className="dataDetail_container"
                    sx={{
                        backgroundColor: commentsMapping(data.id, comments) && '#FDF4F3',
                        border: commentsMapping(data.id, comments) && '1px solid #FBD3CD',
                        borderRadius: '8px',
                        padding: '4px',
                        '& .dataDetail_container': {
                            minHeight: '28px',
                            padding: '4px 0px 4px 0px'
                        }
                    }}
                >
                    {status === 'reject' && (
                        <Grid xs={1} md={1} lg={1} xl={1}>
                            <CommentModal
                                comment={commentsMapping(data.id, comments)}
                                disabled={!commentsMapping(data.id, comments)}
                            />
                        </Grid>
                    )}
                    <Grid xs={3} md={6} lg={5} xl={3}>
                        <Typography variant="body3" sx={{ lineHeight: '20.27px', fontWeight: '600' }}>
                            {detailData.length > 1 ? `${data.title}${i + 1}` : data.title}
                        </Typography>
                    </Grid>
                    <Grid xs={8} md={5} lg={6} xl={8}>
                        <Typography
                            variant="body3"
                            sx={{
                                lineHeight: '1.5', // Sets the height between lines to 1.5 times the font size
                                wordBreak: 'break-all', // Allows words (or characters in CJK) to break at any character
                                wordWrap: 'break-word', // Breaks long words to prevent overflow
                                whiteSpace: 'pre-wrap', // Preserves whitespace and line breaks, but wraps text
                                width: '100%', // Ensures the text takes full width of its container
                                overflowWrap: 'break-word' // Similar to word-wrap, ensures long words don't overflow
                            }}
                        >
                            {data.value}
                        </Typography>
                    </Grid>
                </Grid>
            </AccordionDetails>
        );
    };

    if (isSingleBlock) {
        return Object.values(detailData).map((detail, i) => Object.values(detail).map((d, k) => singleBlock(d, k, i)));
    }

    return (
        <Box sx={{ padding: '0 12px 12px 12px' }}>
            {detailData.map((item, i) => (
                <Accordion
                    // eslint-disable-next-line react/no-array-index-key
                    key={i}
                    className="dataRows"
                    defaultExpanded
                    sx={{
                        '&.dataRows': {
                            marginBottom: '8px',
                            borderRadius: '8px',
                            position: 'unset',
                            borderTopLeftRadius: '8px',
                            borderTopRightRadius: '8px'
                        }
                    }}
                >
                    <AccordionSummary
                        className="dataTilte"
                        expandIcon={<ArrowDropDownIcon />}
                        sx={{
                            '&.dataTilte': {
                                height: '40px',
                                '&.Mui-expanded': {
                                    minHeight: '40px',
                                    backgroundColor: '#F4F8FB',
                                    borderTopRightRadius: '8px',
                                    borderTopLeftRadius: '8px'
                                }
                            },
                            svg: { fill: '#336F89' }
                        }}
                    >
                        <Box
                            sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                width: '100%',
                                alignItems: 'center'
                            }}
                        >
                            <Typography variant="body3" sx={{ fontWeight: '600', color: '#043348' }}>
                                {title}
                                {i + 1}
                            </Typography>
                        </Box>
                    </AccordionSummary>
                    {Object.values([item]).map(detail =>
                        Object.entries(detail).map(([_, d], k) => singleBlock(d, k, i))
                    )}
                </Accordion>
            ))}
        </Box>
    );
};

const TextOutput = ({ checked }) => {
    const [state, _] = useContext(StoreContext);
    const { chatgpt } = state;
    const { isChatgptExtract, outputData, status, serialNumber } = chatgpt;

    const [comments, setComments] = useState([]);

    const getComments = async graph => {
        if (!graph) return [];

        try {
            const res = await axios.get(Api.getCommentsUrl(graph));
            return res.data.data;
        } catch (e) {
            console.error('getComments error: ', e);
            return [];
        }
    };

    const initComments = async graph => {
        if (!graph) return;

        try {
            const res = await getComments(graph);
            setComments(res);
        } catch (e) {
            console.error('initComments error: ', e);
        }
    };

    useEffect(() => {
        if (!serialNumber) return;
        const encodedGraph = Base64.encode(Base64.encode(encodeURI(serialNumber)));

        initComments(encodedGraph);
    }, [serialNumber]);

    return (
        <Grid
            className="output_container"
            sx={{
                '&.output_container': {
                    height: '559px',
                    marginTop: '8px',
                    overflowY: 'scroll'
                }
            }}
        >
            {outputData.map(item => {
                const { sub, title, classType } = item;

                if (classType === 'ReviewEvent' && !(status === '' || status === 'tempSave')) return null;

                return (
                    <Accordion
                        key={item.id}
                        className="dataRows"
                        defaultExpanded
                        sx={{
                            '&.dataRows': {
                                marginBottom: '8px',
                                borderRadius: '8px',
                                position: 'unset',
                                borderTopLeftRadius: '8px',
                                borderTopRightRadius: '8px',
                                marginRight: '16px',
                                '&.Mui-expanded': {
                                    marginBottom: '8px'
                                }
                            },
                            '& .MuiCollapse-wrapperInner': {
                                paddingTop: '1rem'
                            }
                        }}
                    >
                        <AccordionSummary
                            className="dataTitle"
                            expandIcon={<ArrowDropDownIcon />}
                            sx={{
                                '&.dataTitle': {
                                    flexDirection: 'row-reverse',
                                    height: '40px',
                                    '&.Mui-expanded': {
                                        minHeight: '40px',
                                        backgroundColor: '#F4F8FB',
                                        borderTopRightRadius: '8px',
                                        borderTopLeftRadius: '8px'
                                    }
                                },
                                svg: { fill: '#336F89' }
                            }}
                        >
                            <Box
                                sx={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    width: '100%',
                                    alignItems: 'center'
                                }}
                            >
                                <Typography variant="body3" sx={{ fontWeight: '600' }}>
                                    {item.title}
                                </Typography>
                                <PersistentDrawer
                                    isChatgptExtract={isChatgptExtract}
                                    data={sub}
                                    title={title}
                                    classType={classType}
                                    comments={comments}
                                />
                            </Box>
                        </AccordionSummary>
                        <DetailComponent
                            detailData={sub}
                            checked={checked}
                            title={title}
                            classType={classType}
                            comments={comments}
                            status={status}
                        />
                    </Accordion>
                );
            })}
        </Grid>
    );
};

export default TextOutput;
