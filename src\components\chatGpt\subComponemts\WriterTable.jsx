import React, { useState, useEffect, useContext, useMemo, useCallback } from 'react';
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import Paper from '@mui/material/Paper';
import Radio from '@mui/material/Radio';
import { styled } from '@mui/material/styles';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import axios from 'axios';
import { Base64 } from 'js-base64';
import OptimizedAutocomplete from './OptimizedAutocomplete';
import { Api } from '../../../api/Api';
import { isEmpty } from '../../../common/codes';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';

const StyledTableCell = styled(TableCell)(() => ({
    [`&.${tableCellClasses.head}`]: {
        backgroundColor: '#F9FAFC',
        color: '#336F89',
        fontWeight: '600'
    },
    [`&.${tableCellClasses.body}`]: {
        fontSize: 14,
        whiteSpace: 'normal',
        wordBreak: 'break-word',
        overflow: 'hidden',
        // display: '-webkit-box',
        WebkitBoxOrient: 'vertical',
        WebkitLineClamp: 3
    },
    border: '1px solid #E9F1F5',
    borderRadius: '8px'
}));

const StyledTableRow = styled(TableRow)(() => ({
    // '&:nth-of-type(odd)': {
    //     backgroundColor: theme.palette.action.hover
    // }
    // hide last border
    // '&:last-child td, &:last-child th': {
    //     border: 0
    // }
    // border: '1px solid #E9F1F5'
}));

function getValueByKey(data, keyName) {
    // eslint-disable-next-line no-restricted-syntax
    for (const item of data) {
        const matchingItem = item.sub.find(subItem => subItem[keyName]);
        if (matchingItem) {
            return matchingItem[keyName].value;
        }
    }
    return null;
}

const WriterTable = ({ options, outputData, selectedUser, setSelectedUser }) => {
    const [_, dispatch] = useContext(StoreContext);
    const [dropdownSelectedValue, setDropdownSelectedValue] = useState(null);
    const [rows, setRows] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    // Memoize the initial data processing
    const initialData = useMemo(() => {
        const bestKnownName = getValueByKey(outputData, 'bestKnownName');
        return {
            bestKnownName,
            originalName: getValueByKey(outputData, 'originalName__string'),
            penName: getValueByKey(outputData, 'penName__string'),
            hasBirthDate: getValueByKey(outputData, 'hasBirthDate__DateEvent'),
            hasPlaceOfBirth: getValueByKey(outputData, 'hasPlaceOfBirth__Place')
        };
    }, [outputData]);

    const safeEncode = value => (value && value.trim() !== '' ? Base64.encode(value) : Base64.encode('__EMPTY__'));

    // Separate the API call into its own function
    const fetchSimilarData = useCallback(async data => {
        setIsLoading(true);
        try {
            const apiStr = Api.getFindSimilar
                .replace('{query1}', safeEncode(data?.bestKnownName))
                .replace('{query2}', safeEncode(data?.originalName))
                .replace('{query3}', safeEncode(data?.penName));

            const res = await axios.get(apiStr);
            const similarData = res?.data?.data
                .filter(el => el.label !== data.bestKnownName)
                .map(i => ({
                    bestKnownName: i?.label,
                    originalName: i?.originalName,
                    penName: i?.penName,
                    hasBirthDate: i?.hasBirthDate?.replace('DAE_', ''),
                    hasPlaceOfBirth: i?.hasPlaceOfBirth
                }));

            return similarData;
        } catch (error) {
            console.error('Error fetching similar data:', error);
            return [];
        } finally {
            setIsLoading(false);
        }
    }, []);

    // Initial data fetch
    useEffect(() => {
        const loadInitialData = async () => {
            const similarData = await fetchSimilarData(initialData);
            const tmpRows = !isEmpty(similarData)
                ? [initialData, ...similarData, dropdownSelectedValue]
                : [initialData, dropdownSelectedValue];
            setRows(tmpRows);
        };
        loadInitialData();
    }, [initialData, fetchSimilarData]);

    // Handle dropdown changes without re-fetching
    const handleDropdownSelect = useCallback(
        value => {
            if (!value) {
                dispatch({
                    type: Act.CHATGPT_AUTHOR_UPDATE_DRAFT,
                    payload: {}
                });
                setDropdownSelectedValue(null);
                return;
            }

            const dropdownV = {
                bestKnownName: value?.label,
                originalName: value?.originalName,
                penName: value?.penName,
                hasBirthDate: value?.hasBirthDate?.replace('DAE_', ''),
                hasPlaceOfBirth: value?.hasPlaceOfBirth
            };

            dispatch({
                type: Act.CHATGPT_AUTHOR_UPDATE_DRAFT,
                payload: dropdownV
            });
            setDropdownSelectedValue(value);

            // Update the last row without re-fetching
            setRows(prevRows => {
                const newRows = [...prevRows];
                newRows[newRows.length - 1] = dropdownV;
                return newRows;
            });
        },
        [dispatch]
    );

    const handleUserSelect = useCallback(
        (user, row) => {
            setSelectedUser(user);

            if (isEmpty(row)) {
                dispatch({
                    type: Act.CHATGPT_AUTHOR_UPDATE_DRAFT,
                    payload: {}
                });
                return;
            }

            dispatch({
                type: Act.CHATGPT_AUTHOR_UPDATE_DRAFT,
                payload: row
            });
        },
        [dispatch, setSelectedUser]
    );

    // Memoize the table rows
    const memoizedRows = useMemo(() => rows, [rows]);

    return (
        <TableContainer
            component={Paper}
            sx={{
                '&.MuiTableContainer-root': { boxShadow: 'none' },
                maxHeight: '550px'
                // position: 'relative'
            }}
        >
            {isLoading && (
                <Box
                    sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        bottom: 0,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)',
                        zIndex: 1
                    }}
                >
                    <CircularProgress />
                </Box>
            )}
            <Table sx={{ minWidth: 700 }} aria-label="customized table">
                <TableHead>
                    <TableRow>
                        <StyledTableCell />
                        <StyledTableCell sx={{ minWidth: '200px' }}>人物常見名稱</StyledTableCell>
                        <StyledTableCell>本名</StyledTableCell>
                        <StyledTableCell>筆名</StyledTableCell>
                        <StyledTableCell>出生日期</StyledTableCell>
                        <StyledTableCell>出生地</StyledTableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {memoizedRows.map((row, index) => (
                        <StyledTableRow key={`row-${row?.bestKnownName || ''}`}>
                            <StyledTableCell align="center">
                                <Radio
                                    checked={selectedUser === index}
                                    onChange={() => handleUserSelect(index, row)}
                                    value={row?.name || ''}
                                    name="user-radio"
                                    inputProps={{
                                        'aria-label': `Select ${row?.name || ''}`
                                    }}
                                />
                            </StyledTableCell>
                            <StyledTableCell>
                                {index === memoizedRows.length - 1 ? (
                                    <OptimizedAutocomplete
                                        options={options}
                                        handleUserSelect={handleUserSelect}
                                        kIndex={index}
                                        handleDropdownSelect={handleDropdownSelect}
                                        selectedValue={dropdownSelectedValue}
                                    />
                                ) : (
                                    row?.bestKnownName || ''
                                )}
                            </StyledTableCell>
                            <StyledTableCell>{row?.originalName || ''}</StyledTableCell>
                            <StyledTableCell>{row?.penName || ''}</StyledTableCell>
                            <StyledTableCell>{row?.hasBirthDate || ''}</StyledTableCell>
                            <StyledTableCell>{row?.hasPlaceOfBirth || ''}</StyledTableCell>
                        </StyledTableRow>
                    ))}
                </TableBody>
            </Table>
        </TableContainer>
    );
};

export default React.memo(WriterTable);
