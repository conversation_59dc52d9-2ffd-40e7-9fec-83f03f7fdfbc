import React, { useContext } from 'react';
import { Redirect } from 'react-router-dom';

import { isEmpty } from '../common/codes';
import { StoreContext } from '../store/StoreProvider';

// eslint-disable-next-line consistent-return
const RouteProtectedHoc = ({ component: Component, ...rest }) => {
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const isLogin = JSON.parse(localStorage.getItem('hkbdb-gpt-isLogin'));
    const { safeRole } = rest;

    if (!isLogin) {
        return <Redirect to="/signin" />;
    }

    if (safeRole === 'anonymous' && isEmpty(user?.institution)) {
        return <Redirect to="/SignUp/detail" />;
    }

    if (safeRole === 'admin' || safeRole === 'chatgpt') {
        return <Redirect to="/" />;
    }

    if (safeRole !== 'admin' && safeRole !== 'chatgpt') {
        return <Redirect to="/Unauthorized" />;
    }
};

export default RouteProtectedHoc;
