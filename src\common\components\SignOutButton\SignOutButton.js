// import React, { useState } from 'react';
// import { Link } from 'react-router-dom';
//
import './SignOutButton.scss';
//
// import Button from '@mui/material/Button';
// import Menu from '@mui/material/Menu';
// import MenuItem from '@mui/material/MenuItem';
//
//
// // const SignOutButton = ({ setActivePage, item }) => {
// //     // const history = useHistory();
// //     const [anchorEl, setAnchorEl] = useState(null);
// //     const open = Boolean(anchorEl);
// //
// //     const handleClick = event => {
// //         setAnchorEl(event.currentTarget);
// //     };
// //     const handleClose = () => {
// //         setAnchorEl(null);
// //     };
// //
// //     const handleClickSignOut = () => {
// //         setActivePage('');
// //         // history.push("/");
// //         // console.log(item);
// //     };
// //
// //     return (
// //         <>
// //             <Button
// //                 id="basic-button"
// //                 aria-controls={open ? 'basic-menu' : undefined}
// //                 aria-haspopup="true"
// //                 aria-expanded={open ? 'true' : undefined}
// //                 onClick={handleClick}
// //                 className="userNameStyle"
// //             >
// //                 <img src={Icon} alt="icon" style={{ height: '36px', weight: '36px', padding: '0' }} />
// //                 {/* {user.displayName} */}
// //             </Button>
// //             <Menu
// //                 id="basic-menu"
// //                 anchorEl={anchorEl}
// //                 open={open}
// //                 onClose={handleClose}
// //                 MenuListProps={{
// //                     'aria-labelledby': 'basic-button'
// //                 }}
// //                 anchorOrigin={{
// //                     vertical: 'bottom',
// //                     horizontal: 'right'
// //                 }}
// //                 transformOrigin={{
// //                     vertical: 'top',
// //                     horizontal: 'right'
// //                 }}
// //                 sx={{
// //                     '& .MuiPaper-root': { minWidth: '160px' },
// //                     '& .MuiMenu-list': {
// //                         padding: '0'
// //                     },
// //                     '& .MuiPopover-paper': {
// //                         top: '73px !important',
// //                         boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px',
// //                         border: '1px solid #EEEEEE',
// //                         borderRadius: '8px'
// //                     }
// //                 }}
// //             >
// //                 <Link to={item.path} key={item.id} style={{ textDecoration: 'none' }}>
// //                     <MenuItem onClick={handleClickSignOut} className="textColor">
// //                         登出
// //                     </MenuItem>
// //                 </Link>
// //             </Menu>
// //         </>
// //     );
// // };

import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../images/header/User.svg';

const SignOutButton = ({ setActivePage, item }) => {
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [menuTimeout, setMenuTimeout] = useState(null);

    const handleMouseEnter = () => {
        // 清除可能存在的延遲關閉定時器
        if (menuTimeout) {
            clearTimeout(menuTimeout);
        }
        setIsMenuOpen(true);
    };

    const handleMouseLeave = () => {
        // 延遲關閉菜單，避免快速移動導致的閃爍
        const timeout = setTimeout(() => {
            setIsMenuOpen(false);
        }, 200);
        setMenuTimeout(timeout);
    };

    const handleClickSignOut = () => {
        setActivePage('');
    };

    return (
        <div style={{ position: 'relative' }} onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
            <button
                style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer'
                }}
                type="button"
            >
                <img src={Icon} alt="icon" style={{ height: '36px', width: '36px', padding: '0' }} />
            </button>

            {isMenuOpen && (
                <div
                    style={{
                        position: 'absolute',
                        top: '150%',
                        right: 0,
                        backgroundColor: 'white',
                        boxShadow: 'rgba(149, 157, 165, 0.2) 0px 8px 24px',
                        border: '1px solid #EEEEEE',
                        borderRadius: '8px',
                        minWidth: '160px',
                        zIndex: 1000,
                        textAlign: 'start'
                    }}
                    onMouseEnter={handleMouseEnter}
                    onMouseLeave={handleMouseLeave}
                >
                    <Link
                        to={item.path}
                        style={{
                            textDecoration: 'none',
                            color: 'black',
                            // display: 'block',
                            // padding: '10px'
                            fontSize: '14px',
                            fontWeight: '600',
                            display: 'flex',
                            width: '100%',
                            padding: '8px 12px'
                        }}
                        onClick={handleClickSignOut}
                    >
                        登出
                    </Link>
                </div>
            )}
        </div>
    );
};

export default SignOutButton;
