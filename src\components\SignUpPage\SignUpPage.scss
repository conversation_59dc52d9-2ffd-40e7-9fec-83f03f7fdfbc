.signUp{
  max-width: 480px;
}
.signUpPage {
  p,
  h1 {
    margin: 0;
  }
  height: 100vh;
  width: 100%;
  padding: 16px;
  background: linear-gradient(180deg, #f9fafc 0%, #f4f8fb 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .signUpTitle{
    margin-bottom: 1rem;
  }
  .signUpForm {
    //width: 100%;
    display: flex;
    flex-direction: column;
    //padding: 10rem 24rem;
  }
  .signUpField{
    width: 100%;
    margin-bottom: 1rem;
    &-titleBox{
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.5rem;
      &-title{
        font-weight: 600;
        font-size: 14px;
        span{
          color: red;
        }
      }
      &-error{
        font-weight: 600;
        font-size: 14px;
        color: #D14835;
      }
    }
    &-password{
      display: flex;
      position: relative;
      align-items: center;
      justify-content: center;
    }
    .togglePasswordButton {
      display: flex;
      position: absolute;
      right: 12px;
      background: none;
      border: none;
      cursor: pointer;
      font-size: 1rem;
    }
    &-memo{
      font-size: 12px;
      color: #757575;
      margin-top: 0.3rem;
    }
    input{
      width: 100%;
      border-radius: 8px;
      border: 1px solid #eeeeee;
      padding: 8px 12px 8px 16px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20.12px;
    }
  }
  .signUpComment{
    margin-bottom: 2rem;
    p{
      font-size: 14px;
      span{
        color: red;
      }
    }
  }
  .signUpBtnArea{
    display: flex;
    gap: 0.5rem;
    &-back{
      background-color: transparent;
      color: #336F89;
      cursor: pointer;
    }
    &-forward{
      color: #ffffff;
      background-color: #336F89;
      cursor: pointer;
      &-disabled{
        background-color: #F5F5F5;
        cursor: not-allowed;
        color: #BDBDBD;
        border: none !important;
      }
    }
    button{
      width: 50%;
      border: 1px solid #DAE9EF;
      padding: 8px 16px;
      border-radius: 8px;
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.signUpSuceess{
  p,
  h1 {
    margin: 0;
  }
  height: 100vh;
  width: 100%;
  padding-top: 8rem;
  background: linear-gradient(180deg, #f9fafc 0%, #f4f8fb 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  &-container{
    //display: flex;
    //flex-direction: column;
    //justify-content: center;
    //align-items: center;
    //width: 100%;
    min-width: 480px;
    &-box{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      &-content{
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin-top: 1rem;
        width: 100%;
      }
      h1{
        color: #336F89;
        font-size: 24px;
        font-weight: 600;
      }
      p{
        font-size: 14px;
      }
    }
  }
  .signUpBtnArea{
      display: flex;
      gap: 0.5rem;
      width: 100%;
      &-back{
        background-color: transparent;
        color: #336F89;
        cursor: pointer;
      }
      button{
        width: 100%;
        border: 1px solid #DAE9EF;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
      }
  }
}

::-ms-reveal {
  display: none;
}