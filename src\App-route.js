import authority from './App-authority';
import Chatgpt from './components/chatGpt';
import SignInPage from './components/SignInPage';
import SignOutPage from './components/SignOutPage';
import SignUpPage from './components/SignUpPage';
import SignUpDetail from './components/SignUpPage/subPage/SignUpDetail';
import SignUpSuccess from './components/SignUpPage/subPage/SignUpSuccess';
import UnauthorizedPage from './components/UnauthorizedPage';
import WorkArea from './pages/WorkArea';
import AuthRedirectHoc from './routes/AuthRedirectHoc';

export const ROUTE_ID = {
    Home: 'route-Home',
    SignIn: 'route-SignIn',
    SignOut: 'route-SignOut',
    SignUp: 'route-SignUp',
    SignUpDetail: 'route-SignUpDetail',
    SignUpSuccess: 'route-SignUpSuccess',
    Unauthorized: 'route-Unauthorized',
    Auth: 'route-Auth',
    WorkArea: 'route-WorkArea'
};

// authority: 使用者是否可以進入該頁面由 authority 控制
// public: 開發中的頁面, public 設為 false
const routes = [
    {
        id: ROUTE_ID.Home,
        label: 'Home',
        path: '/',
        public: true,
        authority: authority.Home,
        component: Chatgpt
    },
    {
        id: ROUTE_ID.SignIn,
        label: 'Sign In',
        path: '/SignIn',
        public: true,
        authority: authority.SignIn,
        component: SignInPage,
        routeComponent: AuthRedirectHoc
    },
    {
        id: ROUTE_ID.SignUp,
        label: 'SignUp',
        path: '/SignUp',
        public: true,
        authority: authority.SignUp,
        component: SignUpPage,
        routeComponent: AuthRedirectHoc
    },
    {
        id: ROUTE_ID.SignUpDetail,
        label: 'SignUp Detail',
        path: '/SignUp/detail',
        public: true,
        authority: authority.SignUp,
        component: SignUpDetail,
        routeComponent: AuthRedirectHoc
    },
    {
        id: ROUTE_ID.SignUpSuccess,
        label: 'SignUp Success',
        path: '/SignUp/success',
        public: true,
        authority: authority.SignUp,
        component: SignUpSuccess
    },
    {
        id: ROUTE_ID.SignOut,
        label: 'SignOut',
        path: '/SignOut',
        public: true,
        authority: authority.SignOut,
        component: SignOutPage
    },
    {
        id: ROUTE_ID.Unauthorized,
        label: 'Unauthorized',
        path: '/Unauthorized',
        public: true,
        authority: authority.Unauthorized,
        component: UnauthorizedPage,
        routeComponent: AuthRedirectHoc
    },
    // {
    //     id: ROUTE_ID.Auth,
    //     label: "Auth",
    //     path: "/Auth",
    //     public: true,
    //     authority: authority.Unauthorized,
    //     component: ResetPassword,
    // },
    {
        id: ROUTE_ID.WorkArea,
        label: 'WorkArea',
        path: '/WorkArea',
        public: true,
        authority: authority.WorkArea,
        component: WorkArea
    }
];

export const getPathById = routeId => {
    const find = routes.find(o => o.id === routeId);
    return find?.path;
};

export default routes;
