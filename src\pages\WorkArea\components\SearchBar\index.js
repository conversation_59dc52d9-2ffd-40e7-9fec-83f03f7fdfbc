import React, { useState } from 'react';
import './searchBar.scss';
import searchIcon from '../../../../images/search-icon.png';

const SearchBar = ({ onSearch }) => {
    const [query, setQuery] = useState('');

    const handleKeyPress = event => {
        if (event.key === 'Enter') {
            onSearch(query);
        }
    };

    return (
        <div className="searchBar">
            <input
                type="text"
                className="searchInput"
                placeholder="搜尋常見名稱、編號或資料集..."
                onChange={e => setQuery(e.target.value)}
                onKeyDown={handleKeyPress}
            />
            <button
                type="button"
                className="searchBtn"
                onClick={() => onSearch(query)}
            >
                <img src={searchIcon} alt="search" />
            </button>
        </div>
    );
};

export default SearchBar;
