import React, { useContext, useState, useEffect } from 'react';
// css
import './body.scss';
import useTokenReady from '../../hooks/useTokenReady';
import RouterManager from '../../routes/RouterManager';
import { StoreContext } from '../../store/StoreProvider';

const Body = () => {
    const [state] = useContext(StoreContext);
    const { main } = state;

    const { production } = main;
    const [showChildren, setShowChildren] = useState(false);
    // hook for token and locale
    const { tokenReady, axiosReady } = useTokenReady();

    // // 是否為 production mode => production mode 追溯至 firebase 設定
    // eslint-disable-next-line no-unused-vars
    const isProductionMode = _production => _production && _production === 'true';

    // 切換是否顯示 loading 及 FirebaseLayer 的 children
    useEffect(() => {
        if (isProductionMode(production)) {
            if (tokenReady && axiosReady) {
                setShowChildren(true);
            }
        } else {
            setShowChildren(true);
        }
    }, [tokenReady, axiosReady]);

    return <div className="Body">{showChildren && <RouterManager />}</div>;
};

export default Body;
