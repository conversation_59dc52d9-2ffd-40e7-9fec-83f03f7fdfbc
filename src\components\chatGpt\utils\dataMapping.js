import axios from 'axios';
import { Base64 } from 'js-base64';
import cloneDeep from 'lodash.clonedeep';
import defaultEmptyData from './defaultEmptyData';
import { multiType } from './inputType';
import { Api } from '../../../api/Api';
import { isNotEmpty } from '../../../common/codes';
import titleType from '../config/titleType';

// configs

// for gpt mapping
export function mapGptData(gptData) {
    const copyDefaultEmptyData = cloneDeep(defaultEmptyData);

    let authorName = '';
    let originalName = '';
    let nnBestKnownName = '';

    // 處理 NameInformation 和 BasicInformation 抓名稱資料
    // eslint-disable-next-line no-restricted-syntax
    for (const item of copyDefaultEmptyData) {
        if (item.titleEn === 'BasicInformation' || item.titleEn === 'NameInformation') {
            const findGptDataItem = Object.entries(gptData).find(p => p[0].includes(item.titleEn));
            const gptDataSub = findGptDataItem ? findGptDataItem[1] : '';

            if (gptDataSub && gptDataSub.length > 0) {
                const { sub } = item;
                const newSub = [];
                gptDataSub.forEach(f => {
                    Object.values(sub).forEach(i => {
                        const dataF = Object.entries(f);
                        const mergeData = {};
                        Object.entries(i).forEach(property => {
                            const [propertyName, propertyValue] = property;
                            dataF.forEach(df => {
                                if (df[0] === propertyValue.title) {
                                    // 检查df[1]是否是8位数字
                                    if (/^\d{8}$/.test(df[1])) {
                                        const formattedValue = `${df[1].slice(0, 4)}-${df[1].slice(4, 6)}-${df[1].slice(
                                            6,
                                            8
                                        )}`;
                                        mergeData[propertyName] = {
                                            title: df[0],
                                            value: formattedValue
                                        };
                                        // 检查df[1]是否是6位数字
                                    } else if (/^\d{6}$/.test(df[1])) {
                                        const formattedValue = `${df[1].slice(0, 4)}-${df[1].slice(4, 6)}`;
                                        mergeData[propertyName] = {
                                            title: df[0],
                                            value: formattedValue
                                        };
                                    } else {
                                        mergeData[propertyName] = {
                                            title: df[0],
                                            value: df[1]
                                        };
                                    }
                                }
                            });
                        });
                        newSub.push({ ...i, ...mergeData });
                    });
                });
                Object.assign(item.sub, newSub);

                // 抓名稱資料
                if (item.titleEn === 'BasicInformation') {
                    authorName = item?.sub[0]?.bestKnownName?.value || '';
                }
                if (item.titleEn === 'NameInformation') {
                    originalName = item?.sub[0]?.originalName__string?.value || '';
                    nnBestKnownName = item?.sub[0]?.nnBestKnownName__string?.value || '';
                }
            }
        }
    }

    // 再處理其他資料
    // eslint-disable-next-line no-restricted-syntax
    for (const item of copyDefaultEmptyData) {
        // 跳過已經處理過的項目
        if (item.titleEn === 'BasicInformation' || item.titleEn === 'NameInformation') {
            // eslint-disable-next-line no-continue
            continue;
        }

        const findGptDataItem = Object.entries(gptData).find(p => p[0].includes(item.titleEn));
        const gptDataSub = findGptDataItem ? findGptDataItem[1] : '';

        if (gptDataSub) {
            const { sub } = item;
            const newSub = [];
            gptDataSub.forEach(f => {
                Object.values(sub).forEach(i => {
                    const dataF = Object.entries(f);
                    const mergeData = {};
                    Object.entries(i).forEach(property => {
                        const [propertyName, propertyValue] = property;
                        dataF.forEach(df => {
                            if (df[0] === propertyValue.title) {
                                // 檢查df[1]是否是8位數字
                                if (/^\d{8}$/.test(df[1])) {
                                    const formattedValue = `${df[1].slice(0, 4)}-${df[1].slice(4, 6)}-${df[1].slice(
                                        6,
                                        8
                                    )}`;
                                    mergeData[propertyName] = {
                                        title: df[0],
                                        value: formattedValue
                                    };
                                    // 檢查df[1]是否是6位數字
                                } else if (/^\d{6}$/.test(df[1])) {
                                    const formattedValue = `${df[1].slice(0, 4)}-${df[1].slice(4, 6)}`;
                                    mergeData[propertyName] = {
                                        title: df[0],
                                        value: formattedValue
                                    };
                                } else {
                                    mergeData[propertyName] = {
                                        title: df[0],
                                        value: df[1]
                                    };
                                }
                            }
                        });
                    });
                    newSub.push({ ...i, ...mergeData });
                });
            });
            Object.assign(item.sub, newSub);
        }
    }

    // 必填欄位
    // eslint-disable-next-line no-restricted-syntax
    for (const item of copyDefaultEmptyData) {
        if (['Publication', 'Article', 'Pieces'].includes(item.titleEn)) {
            item.sub.forEach(subItem => {
                if (
                    subItem.hasAuthor__Person &&
                    (isNotEmpty(subItem.label_publication__string?.value) ||
                        isNotEmpty(subItem.label_article__string?.value))
                ) {
                    // eslint-disable-next-line no-param-reassign
                    subItem.hasAuthor__Person.value = authorName || originalName || nnBestKnownName;
                } else if (subItem.hasRelatedPerson__Person && isNotEmpty(subItem.label_otherWork__string?.value)) {
                    // eslint-disable-next-line no-param-reassign
                    subItem.hasRelatedPerson__Person.value = authorName || originalName || nnBestKnownName;
                }
            });
        }

        // 沒有常見名稱則將常見名稱補上本名 或 沒有本名則將本名補上常見名稱，確保資料可正確顯示
        if (['BasicInformation', 'NameInformation'].includes(item.titleEn)) {
            item.sub.forEach(subItem => {
                if (subItem.bestKnownName && !isNotEmpty(subItem.bestKnownName?.value)) {
                    // eslint-disable-next-line no-param-reassign
                    subItem.bestKnownName.value = originalName || nnBestKnownName;
                }
                if (subItem.originalName__string && !isNotEmpty(subItem.originalName__string?.value)) {
                    // eslint-disable-next-line no-param-reassign
                    subItem.originalName__string.value = authorName || nnBestKnownName;
                }
                if (subItem.nnBestKnownName__string && !isNotEmpty(subItem.nnBestKnownName__string?.value)) {
                    // eslint-disable-next-line no-param-reassign
                    subItem.nnBestKnownName__string.value = authorName || originalName;
                }
            });
        }
    }

    return copyDefaultEmptyData;
}

export async function getGptData(text) {
    const data = [];
    // 將 getGptReviewSection 從 url 陣列中移除
    const url = [
        Api.getGptNameInfo,
        Api.getGptEducate,
        Api.getGptWork,
        Api.getGptPublication,
        Api.getGptArticle,
        Api.getGptEvent,
        Api.getGptHonor,
        Api.getGptOrganize,
        Api.getGptPiece,
        Api.getGptRelation
    ];

    let resultData = {};

    try {
        const response = await axios.post(
            Api.getGptBaseInfo,
            { default: '', data: text },
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        if (response.status === 200) {
            const { folder_name: folderName } = response.data;
            data.push(response.data.result);

            // 先執行其他 API
            const promises1 = url.map(item =>
                axios.post(
                    item,
                    { default: '', data: text, folder_name: folderName },
                    {
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    }
                )
            );

            const responses1 = await Promise.allSettled(promises1);
            const successfulRes = responses1.filter(res => res.status === 'fulfilled');

            const rest = successfulRes.map(res => res.value.data).filter(res => res.length !== 0);

            // getGptReviewSection必須在其他api跑完後才執行
            const reviewResponse = await axios.post(
                Api.getGptReviewSection,
                { default: '', data: text, folder_name: folderName },
                {
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }
            );

            // 將所有結果合併
            const finalData = data.concat(rest);
            if (reviewResponse.data.length !== 0) {
                finalData.push(reviewResponse.data);
            }

            resultData = Object.assign({}, ...finalData);
        }
        return { message: '擷取成功', data: resultData };
    } catch (e) {
        return { message: '擷取失敗', data: e.response };
    }
}

// for db mapping
function mapDatabaseData(dt, copyDefaultEmptyData) {
    const firstDataId = dt[0].id;
    const copyDefaultEmptyDataSub = copyDefaultEmptyData[firstDataId].sub;
    const rest = dt.filter(dtItem => dtItem.id !== firstDataId);
    const findData = dt[0];
    const { sub: subData } = findData;
    let newData;

    const data = subData.map(items => {
        const { id } = items;
        Object.values(copyDefaultEmptyDataSub).forEach(curSubDatas => {
            const mergeData = {};
            Object.entries(items).forEach(item => {
                Object.entries(curSubDatas).forEach(curSubData => {
                    const [property, dataValue] = curSubData;
                    if (item[0] === curSubData[0]) {
                        mergeData[property] = {
                            id: `${id}___${property}`,
                            title: dataValue.title,
                            value: item[1]
                        };
                    } else if (curSubData[0].includes('label') && item[0].includes('label')) {
                        mergeData[curSubData[0]] = {
                            id: `${id}___${property}`,
                            title: dataValue.title,
                            value: item[1]
                        };
                    }
                });
            });
            newData = { ...curSubDatas, ...mergeData };
        });
        return newData;
    });

    Object.assign(copyDefaultEmptyData[firstDataId], { sub: data });

    if (rest.length !== 0) {
        mapDatabaseData(rest, copyDefaultEmptyData);
    }

    return copyDefaultEmptyData;
}

function encodeEventIds(authorEventIds) {
    return authorEventIds.map(res => {
        const searchEventId = res
            .map(eventId => {
                const { _eventId } = eventId;
                return `hkbdb:${_eventId}`;
            })
            .join(' ');

        const findItem = res[0];
        const { _eventId } = findItem;
        const index = _eventId.indexOf('_');
        const idType = _eventId.slice(0, index);
        const eventIds = Base64.encode(searchEventId);
        return { type: idType, id: eventIds };
    });
}

const evtApiMapping = {
    publication: { getEvtIds: Api.getPublicationEvtIds, getEvt: Api.getPublicationEvt },
    article: { getEvtIds: Api.getArticleEvtIds, getEvt: Api.getArticleEvt },
    otherwork: { getEvtIds: Api.getOtherWorkEvtIds, getEvt: Api.getOtherWorkEvt }
};

function getInformationApi(name, graph, littleClassTypesArray) {
    const result = littleClassTypesArray.map(item => {
        let newItem = '';
        const findTitle = titleType.find(titleTypeItem => item.type === titleTypeItem.titleAbbreviation);
        newItem = Object.assign(item, { classtype: findTitle?.littleClassType });
        return newItem;
    });

    const finalResult = result.map(arr => {
        if (Object.keys(evtApiMapping).includes(arr.classtype)) {
            return evtApiMapping[arr.classtype].getEvt(graph, arr.id);
        }

        if (arr.classtype === 'namenode') {
            return Api.getPersonNameNode(arr.classtype, arr.id, graph);
        }

        return Api.getAllEvent(name, graph, arr.classtype, arr.id);
    });

    finalResult.unshift(Api.getPersonInformation(name, graph));
    return finalResult;
}

function getDataId(eventAbb) {
    const findTitle = titleType.find(item => item.titleAbbreviation === eventAbb);
    return findTitle?.id;
}

// combine property range
function combineData(data) {
    return Object.values(data).map(items => {
        const obj = {};
        items.forEach(item => {
            const { property: basicProperty, range, label_zh: basicData, _personId, _eventId } = item;
            let propertName;

            obj.id = _eventId ? `NER___${_eventId}` : `NER___${_personId}`;

            if (basicProperty === 'bestKnownName') {
                propertName = `${basicProperty}`;
            } else {
                propertName = `${basicProperty}__${range}`;
            }

            const isMultiType = multiType.includes(propertName);

            if (basicData) {
                if (obj[propertName] && (range === 'Place' || range === 'Organization')) {
                    obj[propertName] += `\n${basicData}`;
                } else {
                    obj[propertName] = isMultiType
                        ? `${obj[propertName] ? `${obj[propertName]}\n${basicData}` : basicData}`
                        : basicData;
                }
            } else {
                obj[propertName] = '';
            }
        });
        return obj;
    });
}

const createTableName = id => {
    const findTitle = titleType.find(item => item.id === id);
    if (findTitle) {
        return findTitle.titleEn;
    }
    return '';
};

function sortIdData(restData, infoData) {
    const currentEventArr = restData.shift();
    const { _eventId: currentEvent } = currentEventArr[0];
    const currentEventAbb = currentEvent.slice(0, currentEvent.indexOf('_'));
    const currentEventId = getDataId(currentEventAbb);
    const combineEventData = Object.values(
        currentEventArr.reduce((result, data) => {
            const { _eventId } = data;

            if (currentEventAbb === 'RELEVT') {
                // relationevent data is an one-dimensional array to fix 妻子1、女兒2 on the GPT Extracted Result of table
                // eslint-disable-next-line no-param-reassign
                result.RELEVT = !result.RELEVT ? [data] : [...result.RELEVT, data];
            } else {
                // others are a two-dimensional array
                // eslint-disable-next-line no-param-reassign
                result[`${_eventId}`] = (!result[`${_eventId}`] ? [] : result[`${_eventId}`]).concat(data);
            }

            return result;
        }, {})
    );
    infoData.push({
        tableName: createTableName(currentEventId),
        id: currentEventId,
        sub: combineData(combineEventData)
    });
    if (restData.length !== 0) {
        sortIdData(restData, infoData);
    }
    return infoData;
}

async function getEventIds(authorDataUrls) {
    const promises = authorDataUrls.map(item => axios.get(item));
    const responses = await Promise.allSettled(promises);
    const successfulRes = responses.filter(res => res.status === 'fulfilled');
    return successfulRes.map(res => res.value.data.data).filter(res => res?.length);
}

export async function getTotalInformation(authorName, graph) {
    const copyDefaultEmptyData = JSON.parse(JSON.stringify(defaultEmptyData));
    const baseEncodeAuthorName = Base64.encode(encodeURI(authorName));
    const baseEncodeGraph = Base64.encode(encodeURI(graph));

    const authorDataUrls = titleType.map(item => {
        if (Object.keys(evtApiMapping).includes(item.littleClassType)) {
            return evtApiMapping[item.littleClassType].getEvtIds(baseEncodeGraph);
        }

        if (item.littleClassType !== 'person') {
            return Api.getAllEventIds(baseEncodeAuthorName, baseEncodeGraph, item.littleClassType);
        }
        return null;
    });

    // get event ids
    const authorEventIds = await getEventIds(authorDataUrls);

    // encode event ids
    const encodeAuthorEventIdsList = encodeEventIds(authorEventIds);

    const totalApi = getInformationApi(baseEncodeAuthorName, baseEncodeGraph, encodeAuthorEventIdsList);

    const authorData = await getEventIds(totalApi);
    const [BasicInformation, NameInformation, ...rest] = authorData;
    const infoData = [];
    infoData.push({
        tableName: 'BasicInformation',
        id: 0,
        sub: combineData([BasicInformation])
    });
    infoData.push({
        tableName: 'NameInformation',
        id: 1,
        sub: combineData([NameInformation])
    });
    const result = sortIdData(rest, infoData);

    return mapDatabaseData(result, copyDefaultEmptyData);
}
