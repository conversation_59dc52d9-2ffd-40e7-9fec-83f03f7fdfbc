import React, { useContext, useEffect, useState } from 'react';

// import { ThemeProvider } from "@mui/material/styles";
import { Box, Button as MuiButton } from '@mui/material';
// import theme from "./utils/theme";
import axios from 'axios';
import ChatgptLeft from './subComponemts/ChatgptLeft';
import ChatgptRight from './subComponemts/ChatgptRight';
import Button from './subComponemts/drawer/Button';
import WriterPickerModal from './subComponemts/WriterPickerModal';
import authors from './utils/authors';
import defaultEmptyData from './utils/defaultEmptyData';
import { Api } from '../../api/Api';
import backIcon from '../../images/chatgpt/back.svg';
import Header from '../../pages/Header';
import { StoreContext } from '../../store/StoreProvider';
import './Chatgpt.scss';
import Act from '../../store/actions';

export default function Index() {
    const [state, dispatch] = useContext(StoreContext);
    const { isChatgptExtract, pressSavedToWorkAreaButtonTimes } = state.chatgpt;
    const [isLoading, setIsLoading] = useState(false);
    const [inputText, setInputText] = useState('');
    const [outputData, setOutputData] = useState(defaultEmptyData);
    const [checked, setChecked] = useState(false);
    const [errorMessage, setErrorMessage] = useState('');
    const [authorMenu, setAuthorMenu] = useState('');
    const [writerModalOpen, setWriterModalOpen] = useState(false);
    const [writerOptions, setWriterOptions] = useState([]);
    const [isSaveToWorkArea, setIsSaveToWorkArea] = useState(false);
    const [selectedDataset, setSelectedDataset] = useState('');

    useEffect(() => {
        const getWriterList = async () => {
            await axios.get(Api.getWriterList).then(res => {
                setWriterOptions(res?.data?.data);
            });
        };
        getWriterList();
    }, []);

    return (
        <Box sx={{ height: '100vh' }}>
            <Header />
            <Box className="chatgpt">
                <Box className="chatgpt_container" sx={{ display: 'flex', width: '100%', gap: '1rem' }}>
                    <ChatgptLeft
                        isLoading={isLoading}
                        inputText={inputText}
                        setIsLoading={setIsLoading}
                        setErrorMessage={setErrorMessage}
                        setOutputData={setOutputData}
                        errorMessage={errorMessage}
                        outputData={outputData}
                        authorMenu={authorMenu}
                        setIsSaveToWorkArea={setIsSaveToWorkArea}
                        isSaveToWorkArea={isSaveToWorkArea}
                        selectedDataset={selectedDataset}
                        setSelectedDataset={setSelectedDataset}
                    />
                    <ChatgptRight
                        authors={authors}
                        setOutputData={setOutputData}
                        setChecked={setChecked}
                        checked={checked}
                        outputData={outputData}
                        setInputText={setInputText}
                        setAuthorMenu={setAuthorMenu}
                    />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'flex-between', width: '100%', marginTop: '1rem' }}>
                    {isSaveToWorkArea && (
                        <Box
                            sx={{
                                display: 'flex',
                                width: '100%',
                                marginTop: '1rem',
                                gap: '1rem'
                            }}
                        >
                            <MuiButton
                                sx={{
                                    backgroundColor: 'transparent',
                                    padding: '8px 12px',
                                    border: '1px solid #DAE9EF',
                                    color: '#336F89',
                                    fontSize: '14px',
                                    fontWeight: '600',
                                    borderRadius: '8px',
                                    minWidth: '150px'
                                }}
                            >
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                                    <p>
                                        <img src={backIcon} alt="back" />
                                    </p>
                                    <p> 返回我的工作區</p>
                                </Box>
                            </MuiButton>
                        </Box>
                    )}

                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            width: '100%',
                            marginTop: '1rem',
                            gap: '1rem'
                        }}
                    >
                        <Button
                            clickFunction={() => {
                                dispatch({
                                    type: Act.CHATGPT_PRESS_SAVED_TO_WORK_AREA_BUTTON_TIMES,
                                    payload: pressSavedToWorkAreaButtonTimes + 1
                                });
                                if (!selectedDataset) return;
                                setWriterModalOpen(true);
                            }}
                            isDisabled={!isChatgptExtract}
                            buttonText="暫存至我的工作區"
                            isConfirmation={false}
                        />
                        <Button
                            clickFunction={() => {}}
                            isDisabled={!isChatgptExtract}
                            buttonText="送出審核"
                            isConfirmation
                        />
                    </Box>
                </Box>

                <WriterPickerModal
                    open={writerModalOpen}
                    setOpen={setWriterModalOpen}
                    confirmFct={() => {}}
                    options={writerOptions}
                    setIsSaveToWorkArea={setIsSaveToWorkArea}
                />
            </Box>
        </Box>
    );
}
