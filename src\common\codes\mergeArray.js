import { GV } from './globalVars';
import { isEmpty } from './index';

const defaultOrder = ['property', 'data', 'range', 'graph'];
const symbol = '@with';

const mergeArray = (_data, fieldPropSortedRecords) => {
    // eslint-disable-next-line no-underscore-dangle
    const _mergedData = _data
        .map(item => {
            //
            const itemKeys = Object.keys(item);
            // 將所有的 key 排序
            // 由於資料來源的項目排序都不一樣會造成後續錯誤，必須要排序一次才行
            // 後面程式碼會直接在陣列用 index 取值，所以順序不可以變換
            const sortedKeys = itemKeys.slice(0).sort((a, b) => {
                //
                const regexpIndexOf = (arr, rx) => {
                    // eslint-disable-next-line no-restricted-syntax
                    for (const i in arr) {
                        if (arr[i].toString().match(rx)) {
                            return parseInt(i, 10);
                        }
                    }
                    return -1;
                };
                // eslint-disable-next-line no-shadow
                const newItemKeys = itemKeys.map(item => item.toUpperCase());
                // eslint-disable-next-line no-shadow
                const newOrder = defaultOrder.map(item => newItemKeys[regexpIndexOf(newItemKeys, item.toUpperCase())]);
                //
                const nameA = a.toUpperCase();
                const nameB = b.toUpperCase();
                //
                if (newOrder.indexOf(nameA) < newOrder.indexOf(nameB)) return -1;
                if (newOrder.indexOf(nameA) > newOrder.indexOf(nameB)) return 1;
                return 0;
            });
            // 透過排序過的 key 重新將 item 排好
            return sortedKeys.reduce(
                (prevObj, sortedKey) => ({
                    ...prevObj,
                    [sortedKey]: item[sortedKey]
                }),
                {}
            );
        })
        .map(item => {
            const { graph } = item;
            const combinedStr = Object.keys(item)
                .map(key => key !== 'graph' && item[key])
                // eslint-disable-next-line no-shadow
                .filter(item => item !== false)
                .join(symbol);
            return { graph, combinedStr };
        })
        .reduce((prevObj, item) => {
            const { graph } = item;
            const { combinedStr } = item;
            if (isEmpty(prevObj[combinedStr])) {
                // eslint-disable-next-line no-param-reassign
                prevObj[combinedStr] = [graph];
            } else {
                const graphs = prevObj[combinedStr];
                if (!graphs.includes(graph)) {
                    graphs.push(graph);
                }
            }
            return prevObj;
        }, {});

    // 完成合併後將 key 與 value 還原
    const newData = Object.keys(_mergedData).map(combinedStr => {
        const splitedData = combinedStr.split(symbol);
        return {
            property: splitedData[0],
            data: splitedData[1],
            range: splitedData[2],
            graphs: _mergedData[combinedStr].slice(0).sort()
        };
    });

    // 需求二、將 title與 graph 合併為 key, 找出相同的 key 並合併內容
    const newMergedData = newData
        // to combine title and graph as combinedStrAsKey
        .map(item => {
            const { data } = item;
            const combinedStrAsKey = Object.keys(item)
                .map(key => key !== 'data' && item[key])
                // eslint-disable-next-line no-shadow
                .filter(item => item !== false)
                .join(symbol);
            return { combinedStrAsKey, data };
        })
        // to merge same loader by combinedStrAsKey(title+graph)
        .reduce((md, item) => {
            const { data } = item;
            const { combinedStrAsKey } = item;
            if (isEmpty(md[combinedStrAsKey])) {
                // eslint-disable-next-line no-param-reassign
                md[combinedStrAsKey] = [data];
            } else {
                const allData = md[combinedStrAsKey];
                if (!allData.includes(data)) {
                    allData.push(data);
                }
            }
            return md;
        }, {});

    // 完成合併後將 key 與 value 還原
    const keyMergedData = Object.keys(newMergedData)
        .map(key => {
            const splitedData = key.split(symbol);
            const property = splitedData[0];
            const range = splitedData[1];
            const graphs = splitedData[2].split(',');
            const allData = newMergedData[key];
            return {
                title: property,
                values: allData,
                range,
                dataset: graphs
            };
        })
        // 除去系統的 property
        .filter(row => GV.SYSTEM_HIDE_PROPERTY.indexOf(row.title) < 0);

    // 更改排序 sort([object, object])
    return keyMergedData.slice(0).sort((a, b) => {
        // sort by sorted list
        const order = fieldPropSortedRecords.map(item => (item?.split('__') || [])[0].toUpperCase());
        const nameA = a.title.toUpperCase();
        const nameB = b.title.toUpperCase();
        if (order.indexOf(nameA) < order.indexOf(nameB)) return -1;
        if (order.indexOf(nameA) > order.indexOf(nameB)) return 1;
        return 0;
    });
};

// 根據 id 合併其它所有鍵值為 array
const mergeArrayByKeys = (input, keepKeys, mergedName = 'mergedList') => {
    // 輸入的 input 可能其 parameter 帶 array 或 object
    const strInput = input.map(itm => {
        const newItm = {};
        Object.keys(itm).forEach(k => {
            if (keepKeys.indexOf(k) > -1) {
                newItm[k] = itm[k];
                return;
            }
            newItm[k] = JSON.stringify(itm[k]);
        });
        return newItm;
    });

    const resJson = strInput.reduce((re, obj) => {
        const objKeys = Object.keys(obj);

        const item = re.find(o => {
            const oKeys = Object.keys(o);
            // 如果參數數目不同，直接回傳 null
            if (objKeys.length !== oKeys.length) {
                return null;
            }

            // 判斷兩個 object 是否相同
            let match = true;

            // 不存在此 property
            objKeys.forEach(key => {
                // not found
                if (objKeys.indexOf(key) < 0) {
                    match = false;
                }
                if (oKeys.indexOf(key) < 0) {
                    match = false;
                }

                // 要 merged 的 key 不比較
                if (keepKeys.indexOf(key) > -1) {
                    return;
                }
                // not the same
                if (o[key] !== obj[key]) {
                    match = false;
                }
            });
            return match ? o : null;
        });

        const keepObj = (inObj, tmpKeepKeys) => {
            const mObj = {};
            tmpKeepKeys.forEach(k => {
                mObj[k] = inObj[k];
            });
            return mObj;
        };

        if (item) {
            item[mergedName] = item[mergedName].concat(keepObj(obj, keepKeys));
        } else {
            // eslint-disable-next-line no-param-reassign
            obj[mergedName] = [keepObj(obj, keepKeys)];
            re.push(obj);
        }
        return re;
    }, []);

    return resJson.map(res => {
        const resObj = {};
        // eslint-disable-next-line array-callback-return
        Object.keys(res).map(k => {
            if (mergedName === k || keepKeys.indexOf(k) > -1) {
                resObj[k] = res[k];
                return;
            }
            if (typeof res[k] !== 'string') {
                return;
            }
            resObj[k] = JSON.parse(res[k]);
        });
        return resObj;
    });
};

const arrayMergeData = (input, keys, mergeKeys, mergedName = 'mergedList') =>
    input.reduce((re, obj) => {
        const item = re.find(o => {
            let match = true;

            // 不存在此 property
            keys.forEach(key => {
                // not found
                if (Object.keys(obj).indexOf(key) < 0) {
                    match = false;
                }
                if (Object.keys(o).indexOf(key) < 0) {
                    match = false;
                }

                // not the same
                if (o[key] !== obj[key]) {
                    match = false;
                }
            });
            return match ? o : null;
        });

        const mergedObj = (inObj, inKeys) => {
            const mObj = {};
            inKeys.forEach(k => {
                mObj[k] = inObj[k];
            });
            return mObj;
        };

        if (item) {
            item[mergedName] = item[mergedName].concat(mergedObj(obj, mergeKeys));
        } else {
            // eslint-disable-next-line no-param-reassign
            obj[mergedName] = [mergedObj(obj, mergeKeys)];
            re.push(obj);
        }
        return re;
    }, []);

export { mergeArray, mergeArrayByKeys, arrayMergeData };
