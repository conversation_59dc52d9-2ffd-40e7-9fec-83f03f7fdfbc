import React, { useEffect, useContext } from 'react';
import { getAuth, GoogleAuthProvider, signInWithCredential, fetchSignInMethodsForEmail } from 'firebase/auth';
import { useHistory } from 'react-router-dom';
import { getUser } from '../../../api/firebase/realtimeDatabase';
import { getFormatUser, isEmpty } from '../../../common/codes';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';

// 兩種button皆可使用

// 第一種
// const GoogleButton = ({ setError, setErrorMsg }) => {
//     const [_, dispatch] = useContext(StoreContext);
//     const history = useHistory();
//
//     return (
//         <GoogleLogin
//             text="signin_with"
//             // width="3000px"
//             size="large"
//             // auto_select={false}
//             onSuccess={credentialResponse => {
//                 const decodedToken = jwtDecode(credentialResponse.credential); // Decode the ID token
//                 const { email } = decodedToken; // Extract the email from the token
//                 const auth = getAuth();
//                 fetchSignInMethodsForEmail(auth, email)
//                     .then(providers => {
//                         const [provider] = providers;
//                         if (provider === 'google.com' || !provider) {
//                             const googleCredential = GoogleAuthProvider.credential(credentialResponse.credential);
//                             signInWithCredential(auth, googleCredential)
//                                 .then(userCredential => {
//                                     // authentication user data
//                                     const userInfo = getFormatUser(userCredential.user);
//
//                                     const { uid, displayName } = userInfo;
//
//                                     if (uid && (displayName || email)) {
//                                         dispatch({
//                                             type: Act.FIREBASE_LOGIN_USER,
//                                             payload: userInfo
//                                         });
//
//                                         // firebase realtime user data
//                                         // eslint-disable-next-line consistent-return
//                                         getUser(uid).then(async userData => {
//                                             if (!isEmpty(userData)) {
//                                                 dispatch({
//                                                     type: Act.REGISTER_USER_INFO,
//                                                     payload: userData
//                                                 });
//                                                 dispatch({
//                                                     type: Act.FIREBASE_LOGIN_USER,
//                                                     payload: userData
//                                                 });
//                                                 history.push('/');
//                                             } else {
//                                                 getUser(uid).then(result2 => {
//                                                     dispatch({
//                                                         type: Act.FIREBASE_LOGIN_USER,
//                                                         payload: result2
//                                                     });
//                                                 });
//                                             }
//                                         });
//                                     }
//                                 })
//                                 .catch(error => {
//                                     console.error('Error signing in with Google:', error);
//                                 });
//                         } else {
//                             setError(true);
//                             setErrorMsg('此帳號是通過Email註冊，請使用Email登入');
//                             throw new Error('Invalid login method');
//                         }
//                     })
//                     .catch(error => {
//                         console.error(error);
//                     });
//             }}
//             onError={() => {
//                 console.log('Login Failed');
//             }}
//         />
//     );
// };

// 第二種
const GoogleButton = ({ onSuccess, onError, setError, setErrorMsg }) => {
    const [_, dispatch] = useContext(StoreContext);
    const history = useHistory();
    const handleCredentialResponse = response => {
        try {
            // Decode the JWT token to get user information
            const decoded = response.credential;
            const userObject = JSON.parse(atob(decoded.split('.')[1]));

            const { email } = userObject; // Extract the email from the token
            const auth = getAuth();
            fetchSignInMethodsForEmail(auth, email)
                .then(providers => {
                    const [provider] = providers;
                    if (provider === 'google.com' || !provider) {
                        const googleCredential = GoogleAuthProvider.credential(response.credential);
                        signInWithCredential(auth, googleCredential)
                            .then(userCredential => {
                                // authentication user data
                                const userInfo = getFormatUser(userCredential.user);

                                const { uid, displayName } = userInfo;
                                if (uid && (displayName || email)) {
                                    // firebase realtime user data
                                    // eslint-disable-next-line consistent-return
                                    getUser(uid).then(userData => {
                                        if (!isEmpty(userData)) {
                                            dispatch({
                                                type: Act.FIREBASE_LOGIN_USER,
                                                payload: userData
                                            });
                                            history.push('/');
                                        } else {
                                            getUser(uid).then(result2 => {
                                                if (!isEmpty(result2)) {
                                                    dispatch({
                                                        type: Act.FIREBASE_LOGIN_USER,
                                                        payload: result2
                                                    });
                                                } else {
                                                    dispatch({
                                                        type: Act.FIREBASE_LOGIN_USER,
                                                        payload: userInfo
                                                    });
                                                    history.push('/');
                                                }
                                            });
                                            history.push('/');
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                console.error('Error signing in with Google:', error);
                            });
                    } else {
                        setError(true);
                        setErrorMsg('此帳號是通過Email註冊，請使用Email登入');
                        throw new Error('Invalid login method');
                    }
                })
                .catch(error => {
                    console.error(error);
                });

            // Call the onSuccess callback with user information
            if (onSuccess) {
                onSuccess({
                    id: userObject.sub,
                    name: userObject.name,
                    email: userObject.email,
                    profilePicture: userObject.picture
                });
            }
        } catch (error) {
            // Call the onError callback if something goes wrong
            if (onError) {
                onError(error);
            }
        }
    };

    useEffect(() => {
        const script = document.createElement('script');
        script.src = 'https://accounts.google.com/gsi/client';
        script.async = true;
        script.defer = true;

        document.body.appendChild(script);

        script.onload = () => {
            if (window.google && window.google.accounts) {
                window.google.accounts.id.initialize({
                    client_id: process.env.REACT_APP_GOOGLE_CLIENT_ID,
                    callback: handleCredentialResponse
                });

                window.google.accounts.id.renderButton(document.getElementById('buttonDiv'), {
                    type: 'standard',
                    size: 'large',
                    theme: 'outline',
                    text: 'sign_in_with',
                    shape: 'rectangular',
                    logo_alignment: 'center',
                    // width: '400px' // max 400px
                    width: document.getElementById('parentElement')?.offsetWidth
                });
            }
        };

        return () => {
            document.body.removeChild(script);
            if (window.google && window.google.accounts) {
                window.google.accounts.id.cancel();
            }
        };
    }, []);

    return <div id="buttonDiv" />;
};

export default GoogleButton;
