import { createTheme } from '@mui/material/styles';

const basicTheme = createTheme({
    palette: {
        primary: {
            main: '#3f51b5',
            50: '#f2f5fc',
            300: '#DAE9EF',
            500: '#89ACBB',
            600: '#1D7D9D',
            700: '#3f51b5',
            800: '#215469',
            900: '#323d76'
        },
        secondary: {
            main: '#EEFBFD'
        },
        success: {
            main: '#2E7D32'
        },
        error: {
            main: '#D32F2F'
        },
        warning: {
            main: '#ED6C02'
        },
        info: {
            main: '#0288D1'
        }
    },
    neutral: {
        50: '#FFFFFF',
        100: '#EFEFEF',
        200: '#DCDCDC',
        300: '#BDBDBD',
        400: '#989898',
        500: '#7C7C7C',
        600: '#656565',
        700: '#525252',
        800: '#464646',
        900: '#3D3D3D'
    },
    typography: {
        // fontFamily: ["Noto Sans TC"]
        fontFamily: ['Source Han Serif TC']
        // button: { textTransform: "none" },
    },
    breakpoints: {
        values: {
            xs: 0,
            sm: 576,
            md: 768,
            lg: 992,
            xl: 1200
        }
    }
});

const theme = createTheme(basicTheme, {
    components: {
        MuiTypography: {
            styleOverrides: {
                // h3
                h3: {
                    fontSize: '3rem',
                    fontWeight: 500
                    // lineHeight: 1.235,
                },
                // h4
                h4: {
                    fontSize: '2.125rem',
                    fontWeight: 500
                    // lineHeight: 1.235,
                },
                // h5
                h5: {
                    fontSize: '1.5rem',
                    fontWeight: 500,
                    lineHeight: '34.75px'
                },
                // h6
                h6: {
                    fontSize: '1.25rem',
                    fontWeight: 500
                    // lineHeight: 1.6,
                },
                // subtitle
                subtitle: {
                    fontSize: '1rem',
                    fontWeight: 500,
                    lineHeight: '23.17px'
                },
                // body1
                body1: {
                    fontSize: '1rem',
                    fontWeight: 400
                },
                // body2
                body2: {
                    fontSize: '0.875rem',
                    fontWeight: 400
                    // lineHeight: 1.43,
                },
                body3: {
                    fontSize: '0.875rem', // 14px
                    fontWeight: 500,
                    lineHeight: '20.27px'
                }
            }
        },
        MuiSwitch: {
            styleOverrides: {
                root: {
                    width: '32px',
                    height: '16px',
                    padding: 0
                },
                thumb: { width: '12px', height: '12px', color: '#ffff' },
                switchBase: {
                    padding: '2px',
                    '&.Mui-checked': {
                        transform: 'translateX(16px)'
                    },
                    '&.Mui-checked+.MuiSwitch-track': {
                        opacity: 1
                    }
                },
                track: {
                    borderRadius: '40px'
                }
            }
        },
        MuiFormGroup: {
            styleOverrides: {
                root: {
                    width: '176px',
                    '& .MuiTypography-root': {
                        fontSize: '14px',
                        fontWeight: '500'
                    },
                    '& .MuiFormControlLabel-root': {
                        margin: 0
                    }
                }
            }
        },
        MuiOutlinedInput: {
            styleOverrides: {
                root: {
                    fontSize: '14px',
                    fontWeight: '500'
                },
                notchedOutline: {
                    borderRadius: '8px'
                }
            }
        },
        MuiGrid: {
            styleOverrides: {
                root: {
                    '& ::-webkit-scrollbar': {
                        width: '6px',
                        backgroundColor: '#FAFAFA'
                    },
                    '& ::-webkit-scrollbar-thumb': {
                        backgroundColor: '#BDBDBD'
                    }
                }
            }
        }
    }
});

export default theme;
