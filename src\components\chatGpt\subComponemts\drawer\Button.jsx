import React, { useState, useEffect } from 'react';
import { Button as MuiButton } from '@mui/material';

const confirmButtonStyle = {
    padding: ' 8px 16px',
    '&:hover': {
        backgroundColor: `#2B5F7F`
    },
    backgroundColor: '#336F89',
    color: '#FFFFFF',
    borderRadius: '8px',
    fontWeight: '600',
    fontSize: '14px'
};

const cancelButtonStyle = {
    backgroundColor: '#F4F8FB',
    color: '#336F89',
    padding: '8px 16px',
    borderRadius: '8px',
    fontWeight: '600',
    fontSize: '14px'
};

const disabledButtonStyle = {
    ...confirmButtonStyle,
    backgroundColor: '#F5F5F5',
    color: '#CCCCCC',
    cursor: 'not-allowed',
    opacity: 0.5,
    fontWeight: '600',
    fontSize: '14px'
};

const Button = ({ clickFunction, buttonText, isConfirmation, isDisabled }) => {
    const [buttonComponent, setButtonComponent] = useState(null);

    useEffect(() => {
        const buttonElement = isConfirmation ? (
            <MuiButton
                onClick={clickFunction}
                sx={isDisabled ? disabledButtonStyle : confirmButtonStyle}
                disabled={isDisabled}
            >
                {buttonText}
            </MuiButton>
        ) : (
            <MuiButton
                onClick={clickFunction}
                sx={isDisabled ? disabledButtonStyle : cancelButtonStyle}
                disabled={isDisabled}
            >
                {buttonText}
            </MuiButton>
        );
        setButtonComponent(buttonElement);
    }, [clickFunction, isConfirmation, isDisabled, buttonText]);

    return buttonComponent;
};

export default Button;
