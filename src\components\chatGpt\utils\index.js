import { Base64 } from 'js-base64';
import defaultEmptyData from './defaultEmptyData';
import { createEntry as makeCreateEntry } from './entry';
import Act from '../../../store/actions';
import { createDataToDB, getCoordinates } from '../subComponemts/drawer/utils';

export const clearAll = (dispatch, options = {}) => {
    const { clearNumber = true, clearStatus = true } = options;

    dispatch({ type: Act.CHATGPT_IS_LOADING, payload: true });
    dispatch({
        type: Act.CHATGPT_EXTRACT,
        payload: false
    });
    dispatch({
        type: Act.CHATGPT_INPUT_TEXT,
        payload: ''
    });
    dispatch({
        type: Act.CHATGPT_OUTPUT_DATA,
        payload: defaultEmptyData
    });
    dispatch({ type: Act.CHATGPT_SELECTED_DATASET, payload: '' });
    dispatch({
        type: Act.CHATGPT_IS_SAVE_IN_WORK_AREA,
        payload: false
    });

    if (clearNumber) {
        dispatch({
            type: Act.CHATGPT_NUMBER,
            payload: ''
        });
    }

    if (clearStatus) {
        dispatch({
            type: Act.CHATGPT_STATUS,
            payload: ''
        });
    }

    dispatch({ type: Act.CHATGPT_IS_LOADING, payload: false });
};

const extractUniqueValues = (data, suffix) =>
    Array.from(
        new Set(
            data.flatMap(({ sub }) =>
                sub.flatMap(subitem =>
                    Object.entries(subitem)
                        .filter(([key]) => key.endsWith(suffix))
                        .map(([_, value]) => value?.value)
                        .filter(Boolean)
                )
            )
        )
    );

const filterMissingFromTarget = (source, target) => source.filter(item => !target.some(({ label }) => label === item));

const fetchCoords = async (values, getCoordFn) => Promise.all(values.map(getCoordFn));

const makeCoordEntries = (coords, prefix, type) =>
    coords.map(coord => {
        const id = `${prefix}${Base64.encode(encodeURI(coord.label))}`;
        const newValue = {
            [`label_${type}`]: coord.label,
            geoLatitude: coord.latitude,
            geoLongitude: coord.longitude
        };
        return makeCreateEntry('geo', id, type, newValue);
    });

const createCoordsToDB = async (entries, apiUrl) => Promise.all(entries.map(entry => createDataToDB(apiUrl, entry)));

export const processCoordinates = async (data, type, prefix, allCoords, getGoogleCoord, coordApiUrl) => {
    const values = extractUniqueValues(data, type);
    const notInDb = filterMissingFromTarget(values, allCoords);
    const coords = await fetchCoords(notInDb, getGoogleCoord);
    const entries = makeCoordEntries(coords, prefix, type);
    await createCoordsToDB(entries, coordApiUrl);
};

export const fetchCoordinates = async dispatch => {
    try {
        const plaCoords = await getCoordinates('place');
        const orgCoords = await getCoordinates('organization');

        dispatch({
            type: Act.CHATGPT_ALL_COORDINATES,
            payload: { place: plaCoords || [], organization: orgCoords || [] }
        });
    } catch (e) {
        console.log('Failed to fetch coordinates:', e);
    }
};

export const commentsMapping = (id, comments) => {
    const foundComment = comments.find(comment => id === comment.id);

    if (!foundComment) return '';

    return foundComment.value;
};
