import role from './App-role';

const allPermit = Object.values(role);

const authority = {
    Home: [role.admin, role.chatgpt, role.editor],
    // API: [role.admin, role.developer],
    // 後台
    // BackendWeb: [role.admin, role.developer],
    WorkArea: [role.admin, role.chatgpt, role.editor],
    SignIn: [role.anonymous],
    SignOut: allPermit,
    SignUp: [role.anonymous],
    Unauthorized: [role.anonymous, role.reader, role.developer, role.suggester]
    // Auth:allPermit
};

export default authority;
