import React, { useContext, useState } from 'react';
import {
    getAuth,
    sendPasswordResetEmail,
    signInWithEmailAndPassword,
    // GoogleAuthProvider,
    // signInWithPopup,
    fetchSignInMethodsForEmail
} from 'firebase/auth';
// import firebase from 'firebase/compat/app';
import { useHistory } from 'react-router-dom';
import ErrorModal from './ErrorModal';
import Gbutton from './Gbutton';
import ResetPasswordModal from './ResetPasswordModal';
// import { getUser } from '../../../api/firebase/realtimeDatabase';
import { getFormatUser, isEmpty } from '../../../common/codes';
import handleSignInSuccess from '../../../common/codes/handleSignInSuccess';
import hidePasswordIcon from '../../../images/login_page/hide_password_icon.svg';
import showPasswordIcon from '../../../images/login_page/show_password_icon.svg';
// import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';

const SignInForm = () => {
    const [_, dispatch] = useContext(StoreContext);
    const history = useHistory();
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState(false);
    const [loginEmail, setLoginEmail] = useState('');
    const [password, setPassword] = useState('');
    const [resetPassword, setResetPassword] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const auth = getAuth();

    const togglePasswordVisibility = () => {
        setShowPassword(prevShowPassword => !prevShowPassword);
    };

    // firebaseUiConfig.callbacks = {
    //     signInSuccessWithAuthResult: authResult => {
    //         // authentication user data
    //         const userInfo = getFormatUser(authResult.user);
    //
    //         const { uid, displayName, email } = userInfo;
    //
    //         if (uid && (displayName || email)) {
    //             dispatch({
    //                 type: Act.FIREBASE_LOGIN_USER,
    //                 payload: userInfo
    //             });
    //
    //             // firebase realtime user data
    //             // eslint-disable-next-line consistent-return
    //             getUser(uid).then(async userData => {
    //                 if (!isEmpty(userData)) {
    //                     const { providerId } = userData;
    //                     console.log('providerId', providerId);
    //                     if (providerId === 'password') {
    //                         await auth.signOut();
    //                         setErrorMsg('此Email是透過Email註冊，請改用Email登入。');
    //                         history.push('/signIn');
    //                         return;
    //                     }
    //
    //                     dispatch({
    //                         type: Act.REGISTER_USER_INFO,
    //                         payload: userData
    //                     });
    //                     dispatch({
    //                         type: Act.FIREBASE_LOGIN_USER,
    //                         payload: userData
    //                     });
    //                     history.push('/');
    //                 } else {
    //                     getUser(uid).then(result2 => {
    //                         dispatch({
    //                             type: Act.FIREBASE_LOGIN_USER,
    //                             payload: result2
    //                         });
    //                     });
    //                 }
    //             });
    //         }
    //     }
    // };
    // firebaseUiConfig.signInOptions = [firebase.auth.GoogleAuthProvider.PROVIDER_ID];

    const handleManualSignIn = async e => {
        e.preventDefault();
        setErrorMsg(''); // 清除先前的錯誤訊息
        try {
            const authResult = await signInWithEmailAndPassword(getAuth(), loginEmail, password);
            const userInfo = getFormatUser(authResult.user);
            await handleSignInSuccess(userInfo, dispatch, history);
            // eslint-disable-next-line no-shadow
        } catch (error) {
            if (error.code === 'auth/wrong-password' || error.code === 'auth/user-not-found') {
                // 檢查該 Email 的登入方法
                const signInMethods = await fetchSignInMethodsForEmail(auth, loginEmail);
                if (signInMethods.includes('google.com')) {
                    setErrorMsg('此Email是透過Google註冊，請改用Google登入。');
                } else if (signInMethods.includes('password')) {
                    setErrorMsg('密碼錯誤，請再試一次');
                } else {
                    setErrorMsg('此Email尚未註冊。');
                }
            } else {
                console.error('An error occurred:', error);
            }
            setError(true);
        }
    };

    const handleForgotPassword = async e => {
        if (!loginEmail) {
            setError(true);
            setErrorMsg('請輸入Email');
            return;
        }
        const signInMethods = await fetchSignInMethodsForEmail(auth, loginEmail);
        if (signInMethods.includes('google.com')) {
            setError(true);
            setErrorMsg('此Email是透過Google註冊，不支援重設密碼。');
            return;
        }

        e.preventDefault();
        setErrorMsg('');
        setResetPassword(true);

        try {
            await sendPasswordResetEmail(getAuth(), loginEmail);
            setResetPassword(true);
            // eslint-disable-next-line no-shadow
        } catch (error) {
            console.error('Password reset error:', error);
            setError(true);
            switch (error.code) {
                case 'auth/invalid-email':
                    setErrorMsg('無效的電子郵件地址。請輸入有效的電子郵件。');
                    break;
                case 'auth/user-not-found':
                    setErrorMsg('找不到該用戶。請檢查您的電子郵件地址。');
                    break;
                default:
                    setErrorMsg('重設密碼時出錯。請稍後再試。');
            }
        }
    };

    const handleSignUp = () => {
        history.push('/SignUp');
    };

    return (
        <>
            <h1 className="signInPageTitle">登入</h1>
            <div className="signInPageForm">
                {/* <StyledFirebaseAuth uiConfig={firebaseUiConfig} firebaseAuth={getAuth()} /> */}
                <div id="parentElement" style={{ width: '100%' }}>
                    <Gbutton setError={setError} setErrorMsg={setErrorMsg} />
                </div>
                <form onSubmit={handleManualSignIn} className="sign-in-form">
                    <div className="signInPageField">
                        <p htmlFor="email" className="signInPageLabel">
                            Email
                            <span className="signInPageRequired"> *</span>
                        </p>
                        <input
                            type="email"
                            value={loginEmail}
                            onChange={e => setLoginEmail(e.target.value)}
                            placeholder="Email"
                            className="signInPageInput"
                            required
                        />
                    </div>
                    <div className="signInPageField">
                        <p htmlFor="password" className="signInPageLabel">
                            密碼
                            <span className="signInPageRequired"> *</span>
                        </p>
                        <div className="passwordInputContainer">
                            <input
                                type={showPassword ? 'text' : 'password'}
                                value={password}
                                onChange={e => setPassword(e.target.value)}
                                placeholder="Password"
                                className="signInPageInput"
                                required
                                name="password"
                                id="password"
                                autoComplete="off"
                            />
                            <button
                                type="button"
                                className="togglePasswordButton"
                                onClick={togglePasswordVisibility}
                                aria-label={showPassword ? '隱藏密碼' : '顯示密碼'}
                            >
                                <img
                                    src={showPassword ? hidePasswordIcon : showPasswordIcon}
                                    alt={showPassword ? '隱藏密碼' : '顯示密碼'}
                                    className="passwordIcon"
                                />
                            </button>
                        </div>
                    </div>
                    <div className="forgotPasswordContainer">
                        <button
                            href="#"
                            // className="signInPageForgotPassword"
                            className={`signInPageForgotPassword ${isEmpty(loginEmail) ? 'signInPageForgotPassword-disabled' : ''}`}
                            onClick={handleForgotPassword}
                            type="button"
                            disabled={isEmpty(loginEmail)}
                        >
                            忘記密碼?
                        </button>
                    </div>
                    {error && <p className="error-message">{error}</p>}
                    <button className="signInPageButton signInPageButton--signIn" type="submit">
                        登入
                    </button>
                    <p className="signInPageSeparator">- or -</p>
                    <button
                        className="signInPageButton signInPageButton--register"
                        type="button"
                        onClick={() => handleSignUp()}
                    >
                        註冊帳戶
                    </button>
                </form>
            </div>
            <ResetPasswordModal
                open={resetPassword}
                setOpen={setResetPassword}
                title="已傳送電子郵件"
                content={`我們已經傳送一封電子郵件到 ${loginEmail}，請前往信箱收信並重設密碼。`}
            />
            <ErrorModal open={error} setOpen={setError} title="錯誤" content={errorMsg} />
        </>
    );
};

export default SignInForm;
