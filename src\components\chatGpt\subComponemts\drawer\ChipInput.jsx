import React, { useState, useEffect } from 'react';
import { Chip } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';

const ChipInput = ({ onchangeFct, i, k, data }) => {
    const [receivers, setReceivers] = useState([]);
    const [inputValue, setInputValue] = useState(''); // 輸入的值

    useEffect(() => {
        const initialReceivers = data
            ? data
                  .split('\n')
                  .map(item => item.trim())
                  .filter(item => item)
            : [];
        setReceivers(initialReceivers);
    }, [data]);

    const handleValueChange = value => {
        const uniqueValues = [...new Set(value)];
        const formattedValue = uniqueValues.join('\n');
        setReceivers(uniqueValues);
        onchangeFct(i, k, formattedValue);
    };

    const handleBlur = () => {
        if (inputValue.trim()) {
            const updatedReceivers = [...receivers, inputValue.trim()];
            handleValueChange(updatedReceivers);
            setInputValue(''); // 清空輸入欄位
        }
    };

    return (
        <Autocomplete
            multiple
            id="tags-filled"
            options={[]}
            value={receivers}
            freeSolo
            onChange={(e, value) => handleValueChange(value)}
            inputValue={inputValue} // 綁定輸入欄位值
            onInputChange={(e, value) => setInputValue(value)} // 更新輸入欄位值
            onBlur={handleBlur} // 偵測輸入欄位失焦
            renderOption={(props, option) => (
                <li {...props} key={option}>
                    {option}
                </li>
            )}
            renderTags={(value, getTagProps) =>
                value.map((option, index) => {
                    const { key, ...rest } = getTagProps({ index });
                    return (
                        <Chip
                            // eslint-disable-next-line react/no-array-index-key
                            key={key}
                            {...rest}
                            label={option}
                            sx={{
                                backgroundColor: '#F4F8FB',
                                color: '#336F89',
                                margin: '2px'
                            }}
                        />
                    );
                })
            }
            renderInput={params => (
                <TextField
                    {...params}
                    label=""
                    sx={{
                        '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                                borderColor: '#EEEEEE',
                                border: '1px solid #EEEEEE'
                            },
                            '&:hover fieldset': {
                                borderColor: '#EEEEEE !important'
                            },
                            '&.Mui-focused fieldset': {
                                borderColor: '#EEEEEE !important',
                                border: '1px solid #EEEEEE !important'
                            },
                            '&.Mui-focused': {
                                outline: 'none',
                                boxShadow: 'none'
                            }
                        },
                        '& .MuiOutlinedInput-input': {
                            '&:focus': {
                                outline: 'none',
                                boxShadow: 'none'
                            }
                        }
                    }}
                />
            )}
        />
    );
};

export default ChipInput;
