const genderObj = { Male: 'M', Female: 'F' };
const getGender = genderName => {
    let gender = '';
    if (genderName === '男') {
        gender = genderObj.Male;
    } else if (genderName === '女') {
        gender = genderObj.Female;
    }
    return gender;
};

const getPersonId = (name, personObj) => Object.keys(personObj).indexOf(name) + 1;

const personGedcom = (name, personObj, familyArr) => {
    let gedcomStr = '';

    const personId = getPersonId(name, personObj);
    if (personId === 0) {
        // the name doesn't exist in the perosnObj.
        return gedcomStr;
    }

    const { gender, adopted } = personObj[name];
    // eslint-disable-next-line no-nested-ternary
    const adoptedStr = adopted ? (adopted.length === 0 ? '' : `（${adopted}）`) : '';

    gedcomStr += `0 @I${personId}@ INDI\n`;
    gedcomStr += `1 NAME ${name}${adoptedStr}\n`;
    // nameId
    gedcomStr += `1 NOTE NAMEID=${name}\n`;
    if (gender && gender.length > 0) {
        gedcomStr += `1 SEX ${gender}\n`;
    }

    // FAMS, has how many families.
    const fams = familyArr.map((fam, famId) => {
        if (fam.HUSB === personId || fam.WIFE === personId) {
            return famId;
        }
        return -1;
    });
    fams.forEach(fm => {
        if (fm >= 0) {
            gedcomStr += `1 FAMS @F${fm}@\n`;
        }
    });

    // FAMC, the family comes from.
    const famc = familyArr.map((fam, famId) => {
        if (fam.CHIL && fam.CHIL.find(child => child.id === personId)) {
            return famId;
        }
        return -1;
    });
    famc.forEach(fm => {
        if (fm >= 0) {
            gedcomStr += `1 FAMC @F${fm}@\n`;
        }
    });

    return gedcomStr;
};

const familyGedcom = familyArr => {
    let gedcomStr = '';
    familyArr.forEach((fam, famId) => {
        const { HUSB, WIFE, CHIL } = fam;

        gedcomStr += `0 @F${famId}@ FAM\n`;
        if (HUSB >= 0) {
            gedcomStr += `1 HUSB @I${HUSB}@\n`;
        }
        if (WIFE >= 0) {
            gedcomStr += `1 WIFE @I${WIFE}@\n`;
        }

        if (CHIL) {
            CHIL.forEach(cid => {
                gedcomStr += `1 CHIL @I${cid.id}@\n`;
            });
        }
    });
    return gedcomStr;
};
// {
// relationOP: "hasSon",
// sGender: "男",
// sPerson: "王一賢",
// tGender: "男",
// tPerson: "王雅萱"
// }
const gedcomize = (data, rootName) => {
    // console.log(data);
    const spouseList = ['hasWife', 'hasHusband'];
    const childList = [
        'hasSon',
        'hasDaughter',
        'hasAdoptedDaughter',
        'hasAdoptedSon',
        'hasSonInLaw',
        'hasAdoptedRelativeSon',
        'hasShuSon'
    ];
    // const brotherSisterList = ["hasBrother", "hasBrotherInLaw"];
    const parentList = ['hasFather', 'hasMother', 'hasFatherInLaw'];
    const adoptedObj = {
        hasAdoptedDaughter: '養女',
        hasAdoptedSon: '養子',
        hasSonInLaw: '女婿',
        hasFatherInLaw: '岳父',
        hasAdoptedRelativeSon: '過繼子',
        hasShuSon: '庶子'
    };
    // 取 人物 unique id
    const personObj = {};
    data.forEach(d => {
        const { sPerson, sGender, relationOP, tPerson, tGender } = d;
        const adopted = Object.keys(adoptedObj).indexOf(relationOP) < 0 ? '' : adoptedObj[relationOP];

        if (Object.keys(personObj).indexOf(sPerson) < 0) {
            personObj[sPerson] = { gender: getGender(sGender), adopted: '' };
        }
        if (Object.keys(personObj).indexOf(tPerson) < 0) {
            personObj[tPerson] = { gender: getGender(tGender), adopted };
        }
    });

    // change root id to 0
    // if (rootName && rootName.length > 0) {
    // 	const tmpObj = Object.assign({}, personObj[rootName]);
    // 	delete personObj[rootName];
    //
    // 	const rootObj = {};
    // 	rootObj[rootName] = tmpObj;
    // 	personObj = Object.assign({}, rootObj, personObj);
    // }

    // hasWife, hasHusband 放前面
    data.sort((a, b) => (spouseList.indexOf(a.relationOP) > spouseList.indexOf(b.relationOP) ? -1 : 1));

    // 取 family pair
    // {'HUSB':'xxx', 'WIFE':'ooo', 'CHIL':['aaa', 'bbb', 'ccc']}
    const familyArr = [];
    data.forEach(d => {
        const { sPerson, relationOP, tPerson } = d;

        const sId = getPersonId(sPerson, personObj);
        const sGender = personObj[sPerson].gender;
        const tId = getPersonId(tPerson, personObj);
        const tGender = personObj[tPerson].gender;
        if (spouseList.indexOf(relationOP) >= 0) {
            let husband = -1;
            let wife = -1;

            if (relationOP === 'hasWife') {
                husband = sId;
                wife = tId;
            } else if (relationOP === 'hasHusband') {
                husband = tId;
                wife = sId;
            }

            // console.log('parents: ', sPerson, tPerson);
            // check if the husband/wife pair is in the list
            const foundPair = familyArr.find(fam => {
                if (fam.HUSB === husband && fam.WIFE === -1) {
                    return true;
                }
                if (fam.HUSB === -1 && fam.WIFE === wife) {
                    return true;
                }
                return false;
            });

            // console.log(foundPair);
            if (foundPair) {
                // found
                foundPair.HUSB = husband;
                foundPair.WIFE = wife;
                return;
            }

            // not found
            familyArr.push({ HUSB: husband, WIFE: wife, CHIL: [] });
        } else if (childList.indexOf(relationOP) >= 0 || parentList.indexOf(relationOP) >= 0) {
            let parent = sId;
            let parentGender = sGender;
            let child = tId;
            const adopted = Object.keys(adoptedObj).indexOf(relationOP) < 0 ? '' : adoptedObj[relationOP];

            if (parentList.indexOf(relationOP) >= 0) {
                parent = tId;
                parentGender = tGender;
                child = sId;
            }

            // console.log('parents: ', sPerson, relationOP, tPerson);
            // console.log('parents: ', parent, relationOP, child);
            // check if the husband/wife pair is in the list
            const foundPair = familyArr.find(fam => fam.HUSB === parent || fam.WIFE === parent);

            const childObj = { id: child, type: adopted };
            if (!foundPair) {
                // not found, the parent doesn't exist.
                if (parentGender === genderObj.Male) {
                    // console.log(sPerson, parentGender, tPerson, tGender, relationOP, childObj);
                    familyArr.push({
                        HUSB: parent,
                        WIFE: -1,
                        CHIL: [childObj]
                    });
                } else {
                    // console.log(sPerson, parentGender, tPerson, tGender, relationOP, childObj);
                    familyArr.push({
                        HUSB: -1,
                        WIFE: parent,
                        CHIL: [childObj]
                    });
                }
                return;
            }

            // child not found
            if (foundPair.CHIL.indexOf(childObj) < 0) {
                foundPair.CHIL.push(childObj);
            }
        }
    });
    // console.log(familyArr, personObj);
    // // hasBrother, hasSister
    // familyArr.forEach(fa => {
    //     const { CHIL } = fa;
    //
    //     data.forEach(d => {
    //         const { sPerson, relationOP, tPerson } = d;
    //
    //         const sId = Object.keys(personObj).indexOf(sPerson);
    //         const tId = Object.keys(personObj).indexOf(tPerson);
    //
    //         if (brotherSisterList.indexOf(relationOP) >= 0) {
    //             const foundChil = CHIL.find(c => c.id === sId || c.id === tId);
    //             if (foundChil) {
    //                 const bschild1 = { id: sId, type: "" };
    //                 const bschild2 = { id: tId, type: "" };
    //
    //                 if (CHIL.indexOf(bschild1) < 0) {
    //                     CHIL.push(bschild1);
    //                 }
    //                 if (CHIL.indexOf(bschild2) < 0) {
    //                     CHIL.push(bschild2);
    //                 }
    //             }
    //         }
    //     });
    // });

    // #20210805#, Vincent, Bug Fixed: Duplicated items in Genealogy, for instance, 尹振雄.
    const uniqueFamilyArr = familyArr.reduce((acc, current) => {
        const x = acc.find(item => item.HUSB === current.HUSB && item.WIFE === current.WIFE);
        if (!x) {
            return acc.concat([current]);
        }
        // if (x.CHIL.length < current.CHIL.length) {
        //     console.log(x);
        //     console.log("current:", current);
        // }
        return acc;
    }, []);

    // 將選擇的人名放最上面，才能自動跳轉到該人名。
    let gedcomStr = '';
    gedcomStr += personGedcom(rootName, personObj, uniqueFamilyArr);

    // 該人名不在列表裡，回傳該人物結果
    if (gedcomStr === '') {
        return `0 @I0@ INDI
1 NAME ${rootName}
1 NOTE NAMEID=${rootName}`;
    }

    // data are well prepared. let's generate data for Gedcom.
    // Generate personId list
    Object.keys(personObj).forEach(name => {
        if (name === rootName) {
            return;
        }
        gedcomStr += personGedcom(name, personObj, uniqueFamilyArr);
    });

    // Generate family list
    gedcomStr += familyGedcom(uniqueFamilyArr);

    // console.log(gedcomStr);
    return gedcomStr;
};

export { gedcomize };
