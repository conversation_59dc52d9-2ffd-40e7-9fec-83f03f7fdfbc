{"name": "hkbdb-gptweb", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@firebase/auth": "^1.7.9", "@hookform/resolvers": "^3.9.0", "@mui/icons-material": "^5.15.12", "@mui/lab": "^5.0.0-alpha.168", "@mui/material": "^5.15.12", "@mui/styled-engine-sc": "^6.0.0-alpha.17", "@mui/system": "^5.15.12", "@react-oauth/google": "^0.12.1", "axios": "^1.6.7", "base64url": "^3.0.1", "date-fns": "^4.1.0", "firebase": "^10.13.0", "firebaseui": "^6.1.0", "html2canvas": "^1.0.0-rc.7", "immer": "^10.1.1", "js-base64": "^3.7.7", "jspdf": "^2.3.1", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lodash": "^4.17.20", "lodash.clonedeep": "^4.5.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-datepicker": "^7.5.0", "react-dom": "^18.2.0", "react-hook-form": "^7.53.2", "react-leaflet": "^4.2.1", "react-router": "^5.2.0", "react-router-dom": "^5.3.4", "react-scripts": "5.0.1", "styled-components": "^6.1.8", "twchar": "1.0.22", "uuid": "^11.1.0", "zod": "^3.23.8"}, "devDependencies": {"@babel/eslint-parser": "^7.23.10", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "eslint": "^8.56.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-unused-imports": "^4.1.4", "prettier": "^3.2.5", "sass": "^1.62.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}