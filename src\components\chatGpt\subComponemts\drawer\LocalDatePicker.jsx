import React, { useState, useEffect } from 'react';
import zhTW from 'date-fns/locale/zh-TW';
import DatePicker from 'react-datepicker';
import { isEmpty } from '../../../../common/codes';
import 'react-datepicker/dist/react-datepicker.css';

const LocalDatePicker = ({ onchangeFct, i, k, data }) => {
    const [selectedDate, setSelectedDate] = useState(null);

    function convertToFullDate(dateString) {
        let normalizedDateString;
        if (/^\d{4}-00(-00)?$/.test(dateString)) {
            // 處理 yyyy-00 或 yyyy-00-00，統一視為 yyyy
            normalizedDateString = dateString.slice(0, 4); // 年分
        } else if (/^\d{4}-\d{2}-00$/.test(dateString)) {
            // 處理 yyyy-mm-00，視為該月第一天
            const [year, month] = dateString.split('-').map(Number);
            // if (month === 0 || month > 12) {
            //     throw new Error('Invalid month in yyyy-mm-00 format');
            // }
            normalizedDateString = `${year}-${String(month).padStart(2, '0')}-01`;
        } else if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            // yyyy-mm-dd 格式，檢查月份和日期是否有效
            // const [_, month, day] = dateString.split('-').map(Number);
            // if (month === 0 || month > 12 || day === 0 || day > 31) {
            //     throw new Error('Invalid month or day in yyyy-mm-dd format');
            // }
            normalizedDateString = dateString;
        } else if (/^\d{4}-\d{2}$/.test(dateString)) {
            // yyyy-mm 格式，檢查月份是否有效
            // const [_, month] = dateString.split('-').map(Number);
            // if (month === 0 || month > 12) {
            //     throw new Error('Invalid month in yyyy-mm format');
            // }
            normalizedDateString = `${dateString}-01`; // 補全為 yyyy-mm-01
        } else if (/^\d{4}$/.test(dateString)) {
            // yyyy 格式，直接生成日期
            normalizedDateString = `${dateString}-01-01`;
        } else {
            normalizedDateString = `0000-00-00`;
            // throw new Error('Invalid date format');
        }

        // 使用 Date 物件生成日期
        const date = new Date(`${normalizedDateString}T00:00:00Z`);

        // eslint-disable-next-line no-restricted-globals
        if (isNaN(date.getTime())) {
            // throw new Error('Invalid date');
            return '';
        }

        return date.toString(); // 返回完整的日期字串
    }

    const handleValueChange = value => {
        // const formattedDate = format(value, 'yyyy-MM-dd');

        setSelectedDate(value);
        onchangeFct(i, k, value);
    };

    useEffect(() => {
        // eslint-disable-next-line no-nested-ternary
        const time = isEmpty(data) ? null : typeof data !== 'string' ? data : convertToFullDate(data);
        setSelectedDate(time);
    }, [data]);

    return (
        <DatePicker
            showIcon
            toggleCalendarOnIconClick
            selected={selectedDate}
            onChange={date => handleValueChange(date)}
            placeholderText="YYYY-MM-DD"
            locale={zhTW}
            dateFormat="yyyy/MM/dd"
        />
    );
};

export default LocalDatePicker;
