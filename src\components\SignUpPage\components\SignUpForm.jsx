import React, { useContext, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useHistory } from 'react-router-dom';
import * as z from 'zod';
import hidePasswordIcon from '../../../images/login_page/hide_password_icon.svg';
import showPasswordIcon from '../../../images/login_page/show_password_icon.svg';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';

// 定義驗證模式
const signUpSchema = z
    .object({
        email: z.string().email('請輸入有效的電子郵件地址'),
        password: z
            .string()
            .min(8, '密碼至少需要 8 個字符')
            .regex(
                /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/,
                '密碼必須包含至少一個大寫字母、一個小寫字母和一個數字'
            ),
        confirmPassword: z.string()
    })
    .refine(data => data.password === data.confirmPassword, {
        message: '密碼不符',
        path: ['confirmPassword']
    });

const policyUrl = (
    <span style={{ borderBottom: '1px solid black', cursor: 'pointer', color: 'black' }}>
        https://www.cuhk.edu.hk/policy/pdo/b5/
    </span>
);

const SignUpForm = () => {
    const {
        register,
        handleSubmit,
        watch,
        formState: { errors }
    } = useForm({
        resolver: zodResolver(signUpSchema)
    });
    const watchAllFields = watch();

    const [_, dispatch] = useContext(StoreContext);
    const history = useHistory();
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const onSubmit = data => {
        dispatch({
            type: Act.REGISTER_USER_INFO,
            payload: data
        });
        history.push('/SignUp/detail');
    };

    const togglePasswordVisibility = fct => {
        fct(prevShowPassword => !prevShowPassword);
    };

    const handleBack = () => {
        history.push('SignIn');
    };

    const isFormFilled = () => {
        const requiredFields = ['email', 'password', 'confirmPassword'];
        return requiredFields.every(
            field => watchAllFields[field]?.trim() !== '' && watchAllFields[field] !== undefined
        );
    };

    return (
        <div className="signUp">
            <form onSubmit={handleSubmit(onSubmit)} className="signUpForm">
                <h1 className="signUpTitle">註冊帳戶</h1>
                <div className="signUpComment">
                    <p>現時香港大專院校人士以院校電郵可作自動註冊，其他研究人員註冊將由管理員手動審批。</p>
                </div>
                <div className="signUpField">
                    <div className="signUpField-titleBox">
                        <span className="signUpField-titleBox-title">
                            Email
                            <span>*</span>
                        </span>
                        {errors.email && <span className="signUpField-titleBox-error">*{errors.email.message}</span>}
                    </div>
                    <input {...register('email')} type="email" />
                </div>
                <div className="signUpField">
                    <div className="signUpField-titleBox">
                        <span className="signUpField-titleBox-title">
                            密碼
                            <span>*</span>
                        </span>
                        {errors.password && (
                            <span className="signUpField-titleBox-error">*{errors.password.message}</span>
                        )}
                    </div>
                    <div className="signUpField-password">
                        <input {...register('password')} type={showPassword ? 'text' : 'password'} />
                        <button
                            type="button"
                            className="togglePasswordButton"
                            onClick={() => togglePasswordVisibility(setShowPassword)}
                            aria-label={showPassword ? '隱藏密碼' : '顯示密碼'}
                        >
                            <img
                                src={showPassword ? hidePasswordIcon : showPasswordIcon}
                                alt={showPassword ? '隱藏密碼' : '顯示密碼'}
                                className="passwordIcon"
                            />
                        </button>
                    </div>
                    <p className="signUpField-memo">至少 8 個字元，其中需包括大寫、小寫字母及數字</p>
                </div>
                <div className="signUpField">
                    <div className="signUpField-titleBox">
                        <span className="signUpField-titleBox-title">
                            再次確認密碼
                            <span>*</span>
                        </span>
                        {errors.confirmPassword && (
                            <span className="signUpField-titleBox-error">*{errors.confirmPassword.message}</span>
                        )}
                    </div>
                    <div className="signUpField-password">
                        <input {...register('confirmPassword')} type={showConfirmPassword ? 'text' : 'password'} />
                        <button
                            type="button"
                            className="togglePasswordButton"
                            onClick={() => togglePasswordVisibility(setShowConfirmPassword)}
                            aria-label={showConfirmPassword ? '隱藏密碼' : '顯示密碼'}
                        >
                            <img
                                src={showConfirmPassword ? hidePasswordIcon : showPasswordIcon}
                                alt={showConfirmPassword ? '隱藏密碼' : '顯示密碼'}
                                className="passwordIcon"
                            />
                        </button>
                    </div>
                </div>
                <div className="signUpComment">
                    <p>
                        必需填寫標記星號
                        <span> * </span>
                        的欄位。
                    </p>
                    <p>
                        用戶提供的個人資料均屬自願填寫，並會根據香港中文大學保障個人資料(私穩)的政策( {policyUrl}{' '}
                        )處理。
                    </p>
                </div>
                <div className="signUpBtnArea">
                    <button type="button" className="signUpBtnArea-back" onClick={handleBack}>
                        返回
                    </button>
                    <button
                        type="submit"
                        className={`signUpBtnArea-forward ${!isFormFilled() ? 'signUpBtnArea-forward-disabled' : ''}`}
                        disabled={!isFormFilled()}
                    >
                        下一步
                    </button>
                </div>
            </form>
        </div>
    );
};

export default SignUpForm;
