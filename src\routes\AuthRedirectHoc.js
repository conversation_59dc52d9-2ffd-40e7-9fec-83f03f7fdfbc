import React, { useContext } from 'react';
import { Route, Redirect } from 'react-router-dom';

import { StoreContext } from '../store/StoreProvider';

const AuthRedirectHoc = ({ component: Component, ...rest }) => {
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const isLogin = JSON.parse(localStorage.getItem('hkbdb-gpt-isLogin'));
    const { path } = rest;

    return (
        <Route
            /* eslint-disable-next-line react/jsx-props-no-spreading */
            {...rest}
            render={props => {
                if (isLogin && path === '/Unauthorized') {
                    return <Component {...props} />;
                }

                if (isLogin && path === '/SignUp/detail' && !user?.institution) {
                    return <Component {...props} />;
                }

                if (!isLogin && path === '/SignIn') {
                    return <Component {...props} />;
                }

                if (isLogin) {
                    return <Redirect to="/" />;
                }

                return <Component {...props} />;
            }}
        />
    );
};

export default AuthRedirectHoc;
