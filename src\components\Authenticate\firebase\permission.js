export const permissions = {
    none: 0x0,
    browse: 0x1,
    query: 0x2,
    edit: 0x4,
    confirm: 0x8,
    versioning: 0x10
};

export const roles = {
    guest: 0x0,
    reader: 0x3,
    contributor: 0x7,
    editor: 0xf,
    developer: 0x1f
};

export function getDefaultPermission() {
    return permissions.none;
}

export function getReaderPermission() {
    return roles.reader;
}

export function isAuthed(permission) {
    if (typeof permission === 'undefined') {
        return false;
    }
    return permission > permissions.none;
}

export function isBrowsePermission(permission) {
    // eslint-disable-next-line no-bitwise
    return (permissions.browse & permission) > 0;
}

export function isQueryPermission(permission) {
    // eslint-disable-next-line no-bitwise
    return (permissions.query & permission) > 0;
}

export function isEditPermission(permission) {
    // eslint-disable-next-line no-bitwise
    return (permissions.edit & permission) > 0;
}

export function isAllowed(permission, userPermission) {
    // eslint-disable-next-line no-bitwise
    return (permission & userPermission) > 0;
}

export function isVersioningPermission(permission) {
    // eslint-disable-next-line no-bitwise
    return (permissions.versioning & permission) > 0;
}
