import React, { useState } from 'react';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { Box, Button } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import Grid from '@mui/system/Unstable_Grid';
import ChipInput from './ChipInput';
import CoordinatesModal from './CoordinatesModal';
import CustomDateInput from './CustomDateInput';
import DeleteModal from './DeleteModal';
import { getGoogleCoord } from './utils';
import commentDisabledIcon from '../../../../images/chatgpt/comment-disabled.svg';
import commentIcon from '../../../../images/chatgpt/comment.svg';
import trashIcon from '../../../../images/chatgpt/trash.svg';
import { commentsMapping } from '../../utils';
import { dateType, multiType } from '../../utils/inputType';

const MultiBlock = ({
    classType,
    initialData,
    changeFct,
    title,
    deleteFct,
    openCoordsModal,
    allCoordinates,
    toggleCoordsModal,
    handleChangeCoords,
    savingCoords,
    reviewStatus,
    comments
}) => {
    const [openDelModals, setOpenDelModals] = useState({});

    const handleOpenDelete = index => {
        setOpenDelModals(prev => ({
            ...prev,
            [index]: true
        }));
    };

    const handleCloseDelete = index => {
        setOpenDelModals(prev => ({
            ...prev,
            [index]: false
        }));
    };

    const handleDelete = (index, item) => {
        deleteFct(index, item);
        handleCloseDelete(index);
    };

    return initialData.map((item, index) => (
        <Accordion
            // eslint-disable-next-line react/no-array-index-key
            key={`accordion-${index}`}
            className="dataRows__drawer"
            defaultExpanded
            sx={{
                '&.dataRows__drawer': {
                    marginBottom: '8px',
                    borderRadius: '8px',
                    position: 'unset',
                    borderTopLeftRadius: '8px',
                    borderTopRightRadius: '8px',
                    marginRight: '16px',
                    '&.Mui-expanded': {
                        marginBottom: '8px'
                    }
                },
                '& .MuiCollapse-wrapperInner': {
                    padding: '12px'
                },
                '& .MuiAccordion-region': {
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '1rem'
                }
            }}
        >
            <AccordionSummary
                className="dataTilte"
                expandIcon={<ArrowDropDownIcon />}
                sx={{
                    '&.dataTilte': {
                        flexDirection: 'row-reverse',
                        height: '40px',
                        '&.Mui-expanded': {
                            minHeight: '40px',
                            backgroundColor: '#F4F8FB',
                            borderTopRightRadius: '8px',
                            borderTopLeftRadius: '8px'
                        }
                    },
                    svg: { fill: '#336F89' }
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        width: '100%',
                        alignItems: 'center'
                    }}
                >
                    <Typography variant="body3" sx={{ fontWeight: '600', color: '#043348' }}>
                        {title}
                        {index + 1}
                    </Typography>
                    <Button
                        onClick={e => {
                            e.stopPropagation();
                            handleOpenDelete(index);
                        }}
                        sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                    >
                        <img src={trashIcon} alt="delete" />
                    </Button>
                </Box>
            </AccordionSummary>
            {Object.values([item]).map(detail =>
                Object.entries(detail).map(([key, d], k) => (
                    <AccordionDetails
                        // eslint-disable-next-line react/no-array-index-key
                        key={`detail-${index}-${k}`}
                        className="dataDetail"
                        sx={{
                            '&.dataDetail': {
                                padding: '0'
                            }
                        }}
                    >
                        <Grid
                            container
                            className="dataDetail_container"
                            sx={{
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        >
                            {reviewStatus === 'reject' && (
                                <Grid xs={1}>
                                    <Tooltip title={commentsMapping(d.id, comments)}>
                                        <img
                                            src={commentsMapping(d.id, comments) ? commentIcon : commentDisabledIcon}
                                            alt="comment-icon"
                                        />
                                    </Tooltip>
                                </Grid>
                            )}
                            <Grid xs={3}>
                                <Typography variant="body3" sx={{ lineHeight: '20.27px', fontWeight: '600' }}>
                                    {initialData.length > 1 ? `${d.title}` : d.title}
                                </Typography>
                            </Grid>
                            <Grid xs={8}>
                                {!dateType.includes(key) &&
                                    !multiType.includes(key) &&
                                    !key.endsWith('Place') &&
                                    !key.endsWith('Organization') && (
                                        <TextField
                                            fullWidth
                                            variant="standard"
                                            value={d.value}
                                            onChange={e => changeFct(index, key, e.target.value)}
                                            InputProps={{
                                                disableUnderline: true,
                                                sx: {
                                                    lineHeight: '20.27px',
                                                    borderRadius: '8px',
                                                    border: '1px solid #EEEEEE',
                                                    padding: '8px 12px',
                                                    fontSize: '14px',
                                                    '& .MuiInput-input': {
                                                        padding: '0',
                                                        margin: '0',
                                                        fontFamily: '"Source Han Serif TC", sans-serif'
                                                    }
                                                }
                                            }}
                                        />
                                    )}
                                {multiType.includes(key) && (
                                    <ChipInput onchangeFct={changeFct} i={index} k={key} data={d.value} />
                                )}
                                {dateType.includes(key) && (
                                    <CustomDateInput onchangeFct={changeFct} i={index} k={key} data={d.value} />
                                )}
                                {(key.endsWith('Place') || key.endsWith('Organization')) && (
                                    <CoordinatesModal
                                        open={openCoordsModal[`${classType}-${index}-${k}-${key}`] || false}
                                        setOpen={isOpen =>
                                            toggleCoordsModal(`${classType}-${index}-${k}-${key}`, isOpen)
                                        }
                                        locations={d.value ? d.value.split('\n').filter(value => value !== '') : []}
                                        title={d.title}
                                        coordinateOptions={allCoordinates}
                                        onClick={data => handleChangeCoords(data, index, key)}
                                        getCoordinates={getGoogleCoord}
                                        isLoading={savingCoords}
                                    />
                                )}
                            </Grid>
                        </Grid>
                    </AccordionDetails>
                ))
            )}
            <DeleteModal
                open={openDelModals[index] || false}
                setOpen={isOpen => {
                    if (!isOpen) handleCloseDelete(index);
                }}
                deleteFct={() => handleDelete(index, item)}
            />
        </Accordion>
    ));
};
export default MultiBlock;
