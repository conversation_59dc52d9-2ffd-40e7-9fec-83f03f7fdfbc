# 🚀 快速部署檢查清單

## ⏰ 部署前 15 分鐘檢查

### 📋 基本檢查 (5 分鐘)
```
□ Git 狀態檢查
  □ develop 分支最新代碼已拉取
  □ 沒有未提交的變更
  □ 所有 merge request 已合併

□ 環境檢查
  □ Node.js 版本正確 (檢查 .nvmrc 或 package.json)
  □ npm/yarn 依賴已安裝
  □ 本地建置測試通過 (npm run build)
```

### 🔧 配置檢查 (5 分鐘)
```
□ 環境變數檢查 (.env.production)
  □ REACT_APP_WEB_MODE=production
  □ REACT_APP_API_NODE=https://api.daoyidh.com/hkbdb
  □ REACT_APP_ASK_GPT_API_NODE=https://hkbdb-gpt.daoyidh.com
  □ REACT_APP_GPT_API_NODE=https://api.daoyidh.com/hkbdb-gpt
  □ REACT_APP_GPT_REVIEW_API_NODE=https://api.daoyidh.com/hkbdb-gpt/gptReview
  □ REACT_APP_GOOGLE_CLIENT_ID 正確

□ Firebase 配置檢查
  □ config-firebase.js 指向正式站項目
  □ Firebase Console 權限設定正確
```

### 🗄️ 資料庫檢查 (5 分鐘)
```
□ Firebase Realtime Database
  □ /settings/fieldSetting/production 路徑存在
  □ /settings/swgJSON 角色權限正確
  □ 資料庫規則適用於正式環境

□ API 資料庫
  □ 正式站 API 連接正常
  □ 資料庫遷移（如有）已完成
```

---

## 🚀 部署執行 (10 分鐘)

### 1️⃣ 代碼合併 (3 分鐘)
```bash
# 執行命令
git checkout main
git pull origin main
git merge develop
git push origin main

# 檢查項目
□ 合併成功，無衝突
□ 推送到遠端成功
□ CI/CD 流程觸發（如有）
```

### 2️⃣ 建置部署 (5 分鐘)
```bash
# 執行命令
npm run build
# 上傳 build 資料夾到伺服器

# 檢查項目
□ 建置成功，無錯誤
□ build 資料夾大小合理
□ 檔案上傳完成
□ 伺服器服務重啟（如需要）
```

### 3️⃣ 即時驗證 (2 分鐘)
```
□ 網站可正常訪問
□ 首頁載入正常
□ 登入功能快速測試
□ 主要 API 連接測試
```

---

## ✅ 部署後 10 分鐘檢查

### 🔍 功能驗證 (5 分鐘)
```
□ 用戶登入流程
  □ Google OAuth 登入
  □ 用戶權限驗證
  □ 登出功能

□ 核心功能
  □ 主要頁面載入
  □ API 資料獲取
  □ Firebase 功能
  □ 地圖功能（如適用）
  □ PDF 生成功能（如適用）
```

### 📊 效能檢查 (3 分鐘)
```
□ 頁面載入速度
  □ 首頁 < 3 秒
  □ 主要功能頁面 < 5 秒
  □ API 回應時間 < 2 秒

□ 瀏覽器相容性
  □ Chrome 正常
  □ Firefox 正常
  □ Safari 正常（特別注意）
  □ 行動裝置響應式正常
```

### 🚨 錯誤監控 (2 分鐘)
```
□ 瀏覽器 Console 無錯誤
□ 網路請求無 404/500 錯誤
□ Firebase 連接狀態正常
□ 伺服器日誌無異常
```

---

## 🆘 緊急回滾程序

### ⚡ 立即回滾 (< 5 分鐘)
```
如發現嚴重問題，立即執行：

1. □ 備份當前版本
2. □ 恢復上一穩定版本 build 檔案
3. □ 重啟相關服務
4. □ 通知團隊成員
5. □ 快速功能驗證
```

### 📞 緊急聯絡
```
技術主管：[姓名] - [電話]
系統管理員：[姓名] - [電話]
部署負責人：[姓名] - [電話]
```

---

## 📝 常用命令速查

### Git 操作
```bash
# 檢查分支狀態
git status
git branch -v

# 合併操作
git checkout main
git pull origin main
git merge develop
git push origin main

# 緊急回滾
git reset --hard [上一個穩定版本的 commit hash]
git push origin main --force
```

### 建置操作
```bash
# 清理並重新安裝依賴
rm -rf node_modules package-lock.json
npm install

# 建置正式版本
npm run build

# 檢查建置結果
ls -la build/
du -sh build/
```

### 伺服器操作
```bash
# 上傳檔案（範例）
scp -r build/* user@server:/path/to/web/

# 重啟服務（範例）
sudo systemctl restart nginx
sudo systemctl restart pm2
```

---

## 📋 部署記錄快速模板

```markdown
## 部署記錄 #[編號] - [日期]

**時間：** [開始時間] - [結束時間]
**執行人：** [姓名]
**版本：** [Git Hash]

### 檢查結果
- 部署前檢查：□ 通過 □ 有問題
- 部署執行：□ 成功 □ 失敗
- 部署後驗證：□ 通過 □ 有問題

### 問題記錄
- [問題描述] - 解決方案：[方案] - 負責人：[姓名]

### 備註
[其他需要記錄的資訊]
```

---

**使用說明：**
1. 每次部署前列印此檢查清單
2. 逐項檢查並勾選完成項目
3. 記錄遇到的問題和解決方案
4. 部署完成後歸檔此檢查清單

**最後更新：** [日期] by [更新人員]
