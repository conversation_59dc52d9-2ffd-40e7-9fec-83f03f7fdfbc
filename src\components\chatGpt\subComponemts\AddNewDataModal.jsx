import React from 'react';
import { Box, Modal } from '@mui/material';
import Typography from '@mui/material/Typography';
import Button from './drawer/Button';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 480,
    bgcolor: '#FFFFFF',
    p: 4,
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    padding: 0
};

const titleDivStyle = {
    bgcolor: '#F4F8FB',
    padding: '16px 24px',
    color: '#336F89',
    borderRadius: 2,
    '& h2': {
        fontWeight: 600
    }
};

const contentDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '40px 24px',
    borderRadius: 2,
    '& p': {
        fontSize: '14px',
        textAlign: 'center'
    },
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
};

const buttonDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '0 24px 16px 24px',
    '& p': {
        fontSize: '14px'
    },
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '0.5rem'
};

const AddNewDataModal = ({ open, setOpen, confirmFct }) => {
    const handleClose = () => {
        setOpen(false);
    };

    return (
        <div>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={style}>
                    <Box sx={titleDivStyle}>
                        <Typography id="modal-modal-title" variant="h6" component="h2">
                            新建資料
                        </Typography>
                    </Box>
                    <Box sx={contentDivStyle}>
                        <Typography id="modal-modal-description">
                            資料尚未儲存，確定要清空現有資料，建立全新資料嗎？
                        </Typography>
                    </Box>
                    <Box sx={buttonDivStyle}>
                        <Button
                            clickFunction={() => {
                                handleClose();
                            }}
                            isDisabled={false}
                            buttonText="取消"
                            isConfirmation={false}
                        />
                        <Button
                            clickFunction={() => {
                                handleClose();
                                confirmFct();
                            }}
                            isDisabled={false}
                            buttonText="確定"
                            isConfirmation
                        />
                    </Box>
                </Box>
            </Modal>
        </div>
    );
};

export default AddNewDataModal;
