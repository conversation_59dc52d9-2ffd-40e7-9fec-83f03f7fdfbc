import defaultEmptyData from '../../components/chatGpt/utils/defaultEmptyData';
import Act from '../actions';

const initState = {
    isChatgptExtract: false,
    pressSavedToWorkAreaButtonTimes: 0,
    chatgptDataset: [],
    serialNumber: '',
    status: '',
    modifiedOutputData: [],
    authorUpdateDraft: {},
    inputText: '',
    outputData: defaultEmptyData,
    selectedDataset: '',
    chatGptIsLoading: false,
    isSaveInWorkArea: false,
    allCoordinates: { place: [], organization: [] }
};

// eslint-disable-next-line default-param-last
const chatgptReducer = (state = initState, action) => {
    switch (action?.type) {
        case Act.CHATGPT_EXTRACT:
            return {
                ...state,
                isChatgptExtract: action.payload
            };
        case Act.CHATGPT_PRESS_SAVED_TO_WORK_AREA_BUTTON_TIMES:
            return {
                ...state,
                pressSavedToWorkAreaButtonTimes: action.payload
            };
        case Act.CHATGPT_DATASET:
            return {
                ...state,
                chatgptDataset: action.payload
            };

        case Act.CHATGPT_NUMBER:
            return {
                ...state,
                serialNumber: action.payload
            };
        case Act.CHATGPT_STATUS:
            return {
                ...state,
                status: action.payload
            };
        case Act.CHATGPT_MODIFIED_OUTPUT_DATA:
            return {
                ...state,
                modifiedOutputData: action.payload
            };
        case Act.CHATGPT_AUTHOR_UPDATE_DRAFT:
            return {
                ...state,
                authorUpdateDraft: action.payload
            };
        case Act.CHATGPT_INPUT_TEXT:
            return {
                ...state,
                inputText: action.payload
            };
        case Act.CHATGPT_OUTPUT_DATA:
            return {
                ...state,
                outputData: action.payload
            };
        case Act.CHATGPT_SELECTED_DATASET:
            return {
                ...state,
                selectedDataset: action.payload
            };
        case Act.CHATGPT_IS_LOADING:
            return {
                ...state,
                chatGptIsLoading: action.payload
            };
        case Act.CHATGPT_IS_SAVE_IN_WORK_AREA:
            return {
                ...state,
                isSaveInWorkArea: action.payload
            };
        case Act.CHATGPT_ALL_COORDINATES:
            return {
                ...state,
                allCoordinates: action.payload
            };

        default:
            return state;
    }
};

export default chatgptReducer;
