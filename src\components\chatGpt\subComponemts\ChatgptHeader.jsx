import React from 'react';
import { Box, Button, FormControl, Grid, InputLabel, Select } from '@mui/material';
import MenuItem from '@mui/material/MenuItem';
import '../Chatgpt.scss';

const ChatgptHeader = () => {
    const [age, setAge] = React.useState('');

    const handleChange = event => {
        setAge(event.target.value);
    };

    return (
        <Grid className="chatgptHeader" container sx={{ width: '100%' }}>
            <Grid className="chatgptHeader-left">
                <Button>新建資料</Button>
                <Box sx={{ minWidth: 120 }}>
                    <FormControl fullWidth>
                        <InputLabel id="demo-simple-select-label">Age</InputLabel>
                        <Select
                            labelId="demo-simple-select-label"
                            id="demo-simple-select"
                            value={age}
                            label="Age"
                            onChange={handleChange}
                        >
                            <MenuItem value={10}>Ten</MenuItem>
                            <MenuItem value={20}>Twenty</MenuItem>
                            <MenuItem value={30}>Thirty</MenuItem>
                        </Select>
                    </FormControl>
                </Box>
            </Grid>
            <Grid className="chatgptHeader-right">
                <Box>
                    <p>
                        <span>編號：</span>
                        <span>123</span>
                    </p>
                </Box>
                <Box>
                    <p>
                        <span>審核狀態：</span>
                        <span>123</span>
                    </p>
                </Box>
            </Grid>
        </Grid>
    );
};

export default ChatgptHeader;
