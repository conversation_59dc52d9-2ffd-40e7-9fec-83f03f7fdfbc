import React from 'react';
import { useHistory } from 'react-router-dom';
import SignUpSvg from '../../../images/signUp_page/Vector.svg';

const SignUpSuccess = () => {
    const history = useHistory();
    const handleBack = () => {
        history.push('/SignIn');
    };
    return (
        <div className="signUpSuceess">
            <div className="signUpSuceess-container">
                <div className="signUpSuceess-container-box">
                    <div>
                        <img src={SignUpSvg} alt="check" />
                    </div>
                    <div className="signUpSuceess-container-box-content">
                        <h1>申請完成</h1>
                        <p>管理員審批完成即可開始使用。</p>
                        <div className="signUpBtnArea">
                            <button type="button" className="signUpBtnArea-back" onClick={handleBack}>
                                返回登入畫面
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default SignUpSuccess;
