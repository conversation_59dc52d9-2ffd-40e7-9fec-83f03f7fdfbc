# HKBDB-GPTWeb 部署指南

## 📋 部署檢查清單 (Develop → Main)

### 🔄 部署前準備

#### 1. 代碼檢查
- [ ] 確認 develop 分支所有功能已完成測試
- [ ] 確認所有 merge request 已經過 code review
- [ ] 確認沒有 console.log 或 debug 代碼殘留
- [ ] 執行 `npm run lint` 確保代碼規範
- [ ] 執行測試確保功能正常

#### 2. 環境配置檢查
- [ ] 檢查 `.env.production` 配置是否正確
- [ ] 確認 API 端點指向正式站
  - `REACT_APP_API_NODE=https://api.daoyidh.com/hkbdb`
  - `REACT_APP_ASK_GPT_API_NODE=https://hkbdb-gpt.daoyidh.com`
  - `REACT_APP_GPT_API_NODE=https://api.daoyidh.com/hkbdb-gpt`
  - `REACT_APP_GPT_REVIEW_API_NODE=https://api.daoyidh.com/hkbdb-gpt/gptReview`
- [ ] 確認 `REACT_APP_WEB_MODE=production`
- [ ] 確認 Google OAuth Client ID 正確

#### 3. Firebase 配置檢查
- [ ] 確認 Firebase 配置指向正式站項目
- [ ] 檢查 Firebase Realtime Database 權限設定
- [ ] 確認 Firestore 規則適用於正式環境
- [ ] 檢查 Firebase Authentication 設定

### 🚀 部署流程

#### 1. 分支合併
```bash
# 1. 切換到 main 分支
git checkout main
git pull origin main

# 2. 合併 develop 分支
git merge develop

# 3. 解決衝突（如有）
# 4. 推送到遠端
git push origin main
```

#### 2. 建置部署
- [ ] 執行 `npm run build` 建置正式版本
- [ ] 檢查 build 資料夾大小和內容
- [ ] 部署到正式站伺服器
- [ ] 確認靜態資源正確載入

#### 3. 部署後檢查
- [ ] 檢查網站是否正常載入
- [ ] 測試登入功能
- [ ] 測試主要功能流程
- [ ] 檢查 API 連接是否正常
- [ ] 檢查 Firebase 連接狀態
- [ ] 檢查 Google OAuth 登入
- [ ] 測試地圖功能（Leaflet）
- [ ] 測試 PDF 生成功能

### 🗄️ 資料庫相關操作

#### Firebase Realtime Database
- [ ] 檢查 `/settings/fieldSetting/production` 路徑設定
- [ ] 確認 `/settings/swgJSON` 角色權限設定
- [ ] 檢查 `/settings/webStyle` 樣式配置

#### API 資料庫
- [ ] 確認正式站資料庫連接正常
- [ ] 檢查資料庫版本是否需要更新
- [ ] 確認資料遷移（如有）已完成

### 🔧 Nginx 配置檢查
- [ ] 確認 Nginx 配置指向正確的 build 資料夾
- [ ] 檢查 SSL 憑證是否有效
- [ ] 確認 gzip 壓縮設定
- [ ] 檢查快取設定
- [ ] 測試 SPA 路由重定向規則

---

## 📝 部署記錄模板

### 部署記錄 #[編號] - [日期]

**部署時間：** YYYY-MM-DD HH:MM  
**執行人員：** [姓名]  
**版本：** [Git Commit Hash]  
**部署類型：** [ ] 功能更新 [ ] 緊急修復 [ ] 安全更新

#### 本次更新內容
- [ ] 功能1描述
- [ ] 功能2描述
- [ ] Bug修復描述

#### 環境變更
- [ ] **資料庫：** [變更描述] - 負責人：[姓名] - 時間：[時間]
- [ ] **Nginx：** [變更描述] - 負責人：[姓名] - 時間：[時間]
- [ ] **Firebase：** [變更描述] - 負責人：[姓名] - 時間：[時間]
- [ ] **API：** [變更描述] - 負責人：[姓名] - 時間：[時間]

#### 部署步驟執行狀況
- [ ] 代碼合併 - 執行人：[姓名] - 時間：[時間]
- [ ] 環境配置檢查 - 執行人：[姓名] - 時間：[時間]
- [ ] 建置部署 - 執行人：[姓名] - 時間：[時間]
- [ ] 功能測試 - 執行人：[姓名] - 時間：[時間]

#### 遇到的問題
- **問題1：** [問題描述]
  - **解決方案：** [解決方法]
  - **負責人：** [姓名]
  - **解決時間：** [時間]

#### 部署後驗證
- [ ] 網站載入正常
- [ ] 登入功能正常
- [ ] API 連接正常
- [ ] Firebase 功能正常
- [ ] 主要業務流程測試通過

**部署完成時間：** YYYY-MM-DD HH:MM  
**總耗時：** [時間]

---

## ⚠️ 常見問題與解決方案

### 1. 環境變數問題
**問題：** 部署後 API 連接失敗  
**原因：** `.env.production` 配置錯誤  
**解決：** 檢查環境變數是否正確設定，重新建置部署

### 2. Firebase 連接問題
**問題：** Firebase 功能異常  
**原因：** Firebase 配置或權限問題  
**解決：** 檢查 `config-firebase.js` 和 Firebase Console 設定

### 3. 靜態資源載入失敗
**問題：** CSS/JS 檔案 404 錯誤  
**原因：** Nginx 配置或路徑問題  
**解決：** 檢查 Nginx 配置和 build 資料夾路徑

### 4. SPA 路由問題
**問題：** 重新整理頁面出現 404  
**原因：** Nginx 未正確配置 SPA 重定向  
**解決：** 確認 Nginx 配置包含 `try_files $uri $uri/ /index.html;`

---

## 👥 責任分工

### 🔧 技術負責人
- **前端部署：** [負責人姓名]
- **後端 API：** [負責人姓名]
- **資料庫：** [負責人姓名]
- **Nginx/伺服器：** [負責人姓名]
- **Firebase：** [負責人姓名]

### 📋 檢查負責人
- **代碼審查：** [負責人姓名]
- **功能測試：** [負責人姓名]
- **安全檢查：** [負責人姓名]

---

## 🔄 回滾計劃

### 緊急回滾步驟
1. **立即回滾：** 恢復上一版本的 build 檔案
2. **資料庫回滾：** 如有資料庫變更，執行回滾腳本
3. **通知團隊：** 立即通知相關人員
4. **問題追蹤：** 記錄問題並安排修復

### 回滾檢查清單
- [ ] 備份當前版本
- [ ] 恢復上一穩定版本
- [ ] 測試核心功能
- [ ] 確認資料完整性
- [ ] 通知使用者（如需要）

---

## 📞 緊急聯絡資訊

- **技術主管：** [姓名] - [電話] - [Email]
- **系統管理員：** [姓名] - [電話] - [Email]
- **資料庫管理員：** [姓名] - [電話] - [Email]

---

*最後更新：[日期] by [更新人員]*
