import Act from '../actions';

const initState = {
    authFinished: false,
    production: 'true' // 預設為 true
};

// eslint-disable-next-line default-param-last
const mainReducer = (state = initState, action) => {
    switch (action?.type) {
        case Act.AUTH_FINISHED:
            return {
                ...state,
                authFinished: action.payload
            };

        case Act.SET_PRODUCTION:
            return {
                ...state,
                production: action.payload
            };
        default:
            return state;
    }
};

export default mainReducer;
