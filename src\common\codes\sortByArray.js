import { sortedByStroke } from 'twchar';

const sortByArray = (inputArr, keyArr) => {
    if (!inputArr || inputArr.length === 0) {
        return [];
    }

    const keyElse = '@KeyElse';
    const resObj = { [keyElse]: [] };

    inputArr.forEach(item => {
        // eslint-disable-next-line no-restricted-syntax
        for (const key in keyArr) {
            if (Object.keys(item).indexOf(key) > -1) {
                // include
                if (Object.keys(resObj).indexOf(key) > -1) {
                    resObj[key].append(item);
                } else {
                    resObj[key] = [item];
                }
                return;
            }
        }
        resObj[keyElse].append(item);
    });

    // sort
    let sortedArr = [];
    Object.keys(resObj).forEach(key => {
        const tmpKeyArr = sortedByStroke(resObj[key], [key]);
        sortedArr = sortedArr.concat(tmpKeyArr);
    });
    return sortedArr;
};

export { sortByArray };
