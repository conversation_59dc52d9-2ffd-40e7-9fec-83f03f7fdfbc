import React, { useState, useContext, useMemo } from 'react';
import { Box, Modal } from '@mui/material';
import Typography from '@mui/material/Typography';
import { isEmpty } from 'lodash';
import Button from './drawer/Button';
import WriterTable from './WriterTable';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: '70vw',
    bgcolor: '#FFFFFF',
    p: 4,
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    padding: 0
};

const titleDivStyle = {
    bgcolor: '#F4F8FB',
    padding: '16px 24px',
    color: '#336F89',
    borderRadius: 2,
    '& h2': {
        fontWeight: 600
    }
};

const contentDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '40px 24px',
    borderRadius: 2,
    '& p': {
        fontSize: '14px',
        textAlign: 'center'
    },
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'column',
    gap: '1rem'
};

const buttonDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '0 24px 16px 24px',
    '& p': {
        fontSize: '14px'
    },
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '0.5rem'
};

const WriterPickerModal = ({ open, setOpen, confirmFct, options, outputData, writerPickerModalType, onClose }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { authorUpdateDraft } = state.chatgpt;

    const [selectedUser, setSelectedUser] = useState(0);
    const noAuthorData = useMemo(() => isEmpty(authorUpdateDraft), [authorUpdateDraft]);

    const handleClose = () => {
        setSelectedUser(0);
        setOpen(false);
        if (onClose) onClose();
    };

    return (
        <Modal
            open={open}
            onClose={handleClose}
            aria-labelledby="modal-modal-title"
            aria-describedby="modal-modal-description"
        >
            <Box sx={style}>
                <Box sx={titleDivStyle}>
                    <Typography id="modal-modal-title" variant="h6" component="h2">
                        資料集作家選擇
                    </Typography>
                </Box>
                <Box sx={contentDivStyle}>
                    <Box
                        sx={{
                            width: '100%',
                            display: 'flex',
                            justifyContent: 'flex-start'
                        }}
                    >
                        <Typography id="modal-modal-description">
                            請確認要將資料導入至HKBDB資料庫中的哪位作家。
                        </Typography>
                    </Box>
                    <Box sx={{ width: '100%' }}>
                        <WriterTable
                            options={options}
                            outputData={outputData}
                            selectedUser={selectedUser}
                            setSelectedUser={setSelectedUser}
                        />
                    </Box>
                </Box>
                <Box sx={buttonDivStyle}>
                    <Button
                        clickFunction={() => {
                            handleClose();
                        }}
                        isDisabled={false}
                        buttonText="取消"
                        isConfirmation={false}
                    />
                    <Button
                        clickFunction={() => {
                            if (noAuthorData) return;
                            handleClose();
                            confirmFct();
                            dispatch({
                                type: Act.CHATGPT_IS_SAVE_IN_WORK_AREA,
                                payload: true
                            });
                        }}
                        isDisabled={noAuthorData}
                        buttonText={writerPickerModalType === 'save' ? '送出審核' : '暫存至我的工作區'}
                        isConfirmation
                    />
                </Box>
            </Box>
        </Modal>
    );
};

export default WriterPickerModal;
