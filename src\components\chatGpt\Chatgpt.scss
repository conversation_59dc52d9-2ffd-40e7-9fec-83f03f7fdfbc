.chatgpt {
  padding: 32px 80px 32px 80px;
  //width: 1200px;
  //height: 755px;
  //margin: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background-color: #F9FAFC;
  //height: 90vh;
  flex-direction: column;
  //&_container {
  //  height: 600px;
  //  width: 1120px;
  //  column-gap: 16px;
  //}
  &_output {
    justify-content: space-between;
    align-items: center;
  }
}

.react-datepicker-wrapper{
  width: 100%;
  .react-datepicker__input-container{
    //padding:8px 12px;
    width: 100%;
    .react-datepicker__calendar-icon{
      right: 0;
    }
    input{
      width: 100%;
      border: 1px solid #EEEEEE;
      border-radius: 8px;
      padding: 8px 12px;
      font-size: 14px;
    }
  }
}

.dataTitle .accordion-button {
  visibility: hidden;
  opacity: 0;
  transition: visibility 0.2s, opacity 0.2s;
}

.dataTitle:hover .accordion-button,
//.dataTitle:focus-within .accordion-button
{
  visibility: visible;
  opacity: 1;
}