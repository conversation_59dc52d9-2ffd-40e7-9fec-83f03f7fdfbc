import React, { useContext, useEffect, useState } from 'react';
import LoadingButton from '@mui/lab/LoadingButton';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
import Box from '@mui/system/Box';
import { Api } from '../../../api/Api';
import Act from '../../../store/actions';
import { StoreContext } from '../../../store/StoreProvider';
import { fetchCoordinates, processCoordinates } from '../utils';
import { mapGptData, getGptData } from '../utils/dataMapping';
import { getGoogleCoord } from './drawer/utils';

const MAXLIMIT = 3000;

const TextInput = ({ isLoading, errorMessage, setIsLoading, setErrorMessage, authorMenu }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { chatgpt } = state;
    const { inputText, status, allCoordinates } = chatgpt;

    const [initInput, setInitInput] = useState('');

    const handleRetrieveClick = async () => {
        setIsLoading(true);

        try {
            // 在執行擷取前先轉換全形空白為半形空白(原始資料才可抓到正確的段落)
            const convertedInput = initInput.replace(/\u3000/g, ' ');
            setInitInput(convertedInput);

            const result = await getGptData(convertedInput);
            if (!result) {
                setErrorMessage('無法取得 GPT 資料');
                return;
            }

            const data = mapGptData(result.data);
            dispatch({ type: Act.CHATGPT_OUTPUT_DATA, payload: data });

            const url = Api.restfulHKBDB;

            await Promise.all([
                processCoordinates(data, 'Place', 'PLA', allCoordinates.place, getGoogleCoord, url),
                processCoordinates(data, 'Organization', 'ORG', allCoordinates.organization, getGoogleCoord, url)
            ]);

            await fetchCoordinates(dispatch);

            dispatch({ type: Act.CHATGPT_EXTRACT, payload: true });
        } catch (e) {
            console.error(e);
            setErrorMessage('擷取過程中發生錯誤');
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        setInitInput(inputText);
    }, [inputText]);

    return (
        <>
            <Box sx={{ height: '504px' }}>
                <TextField
                    className="textInput"
                    sx={{
                        '&.textInput': {
                            height: '480px',
                            '& fieldset': {
                                height: '480px'
                            }
                        }
                    }}
                    fullWidth
                    placeholder="輸入文字"
                    value={initInput}
                    multiline
                    id="fullWidth"
                    rows={22}
                    onChange={e => setInitInput(e.target.value)}
                    onBlur={e =>
                        dispatch({
                            type: Act.CHATGPT_INPUT_TEXT,
                            payload: e.target.value
                        })
                    }
                    disabled={!(status === '' || status === 'tempSave' || status === 'reject')}
                />

                <Typography
                    variant="body2"
                    sx={{
                        textAlign: 'right',
                        color: `${initInput.length < MAXLIMIT ? '#9E9E9E' : 'warning.main'}`
                    }}
                >
                    {errorMessage} 已貼入{initInput.length}字/上限{MAXLIMIT}字
                </Typography>
            </Box>
            {/* 擷取 */}
            <LoadingButton
                loading={isLoading}
                fullWidth
                variant="contained"
                disabled={
                    initInput.length > MAXLIMIT ||
                    initInput.length === 0 ||
                    authorMenu !== '' ||
                    !(status === '' || status === 'tempSave' || status === 'reject')
                }
                onClick={() => handleRetrieveClick()}
                sx={{
                    backgroundColor: '#336F89',
                    '&:hover': {
                        backgroundColor: '#2B5F7F'
                    }
                }}
            >
                擷取
            </LoadingButton>
        </>
    );
};

export default TextInput;
