import React, { useState, useEffect } from 'react';
import L from 'leaflet';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, useMap } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import './map.css';

const RecenterView = ({ center }) => {
    const map = useMap();
    useEffect(() => {
        map.setView(center);
    }, [center, map]);
    return null;
};

const Map = ({ editCoords = [], sx, mapPinIcon, defaultCenter, defaultZoom }) => {
    const [center, setCenter] = useState(defaultCenter);

    const customIcon = new L.Icon({
        iconUrl: mapPinIcon,
        iconSize: [32, 32],
        iconAnchor: [16, 16]
    });

    useEffect(() => {
        if (!editCoords.length) return;

        const lastCoord = editCoords[editCoords.length - 1]?.new;

        if (lastCoord?.latitude && lastCoord?.longitude && lastCoord.latitude !== '-' && lastCoord.longitude !== '-') {
            const newCenter = [lastCoord.latitude, lastCoord.longitude];
            setCenter(newCenter);
        }
    }, [editCoords]);

    return (
        <MapContainer center={center} zoom={defaultZoom} style={sx}>
            <TileLayer
                url="https://tiles.stadiamaps.com/tiles/alidade_smooth/{z}/{x}/{y}{r}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors &copy; <a href="https://stadiamaps.com/">Stadia Maps</a>'
            />
            <RecenterView center={center} />
            {editCoords
                .filter(
                    location =>
                        location?.new?.label &&
                        location?.new?.latitude &&
                        location?.new?.longitude &&
                        location?.new?.latitude !== '-' &&
                        location?.new?.longitude !== '-'
                )
                .map(location => (
                    <Marker
                        key={location.id}
                        position={[location.new.latitude, location.new.longitude]}
                        icon={customIcon}
                    >
                        <Tooltip direction="top" offset={[0, -16]} opacity={1} className="mapTooltip">
                            {location.new.label}
                        </Tooltip>
                    </Marker>
                ))}
        </MapContainer>
    );
};

export default Map;
