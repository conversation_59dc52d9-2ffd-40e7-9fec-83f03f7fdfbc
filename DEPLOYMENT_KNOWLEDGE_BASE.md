# 🧠 部署知識庫

## 🔧 環境配置詳解

### 測試站 vs 正式站配置對比

| 配置項目 | 測試站 (Development) | 正式站 (Production) |
|---------|---------------------|-------------------|
| **API 端點** | `https://api2.daoyidh.com/hkbdb3` | `https://api.daoyidh.com/hkbdb` |
| **GPT API** | `https://api2.daoyidh.com/hkbdb-gpt3` | `https://api.daoyidh.com/hkbdb-gpt` |
| **Ask GPT** | `https://hkbdb-gpt2.daoyidh.com` | `https://hkbdb-gpt.daoyidh.com` |
| **GPT Review** | `https://api2.daoyidh.com/hkbdb-gpt3/gptReview` | `https://api.daoyidh.com/hkbdb-gpt/gptReview` |
| **Web Mode** | `development` | `production` |
| **Firebase DB Path** | `/settings/fieldSetting/development` | `/settings/fieldSetting/production` |

### 🔑 重要配置說明

#### Firebase 配置
```javascript
// src/api/config/config-firebase.js
const firebaseConfig = {
    apiKey: 'AIzaSyBgbA6Y50A8bsrCnbuCsqPuHFzsysv8INo',
    authDomain: 'hkbdb-web.firebaseapp.com',
    databaseURL: 'https://hkbdb-web-default-rtdb.firebaseio.com',
    projectId: 'hkbdb-web',
    // ... 其他配置
};
```

#### 環境變數優先級
1. `.env.production` (正式站)
2. `.env.development` (測試站)
3. `.env.local` (本地覆蓋，不應提交到 Git)

---

## ⚠️ 常見問題解決方案

### 1. 🔌 API 連接問題

#### 問題：API 請求失敗或超時
**症狀：**
- 頁面載入後無資料顯示
- Console 出現 CORS 錯誤
- 網路請求返回 404/500 錯誤

**排查步驟：**
1. 檢查 `.env.production` 中的 API 端點是否正確
2. 確認 API 伺服器是否正常運行
3. 檢查網路連接和防火牆設定
4. 驗證 API 端點的 SSL 憑證

**解決方案：**
```javascript
// 在 src/api/Api.js 中檢查
console.log('Current API Base URL:', baseUrl);
console.log('Environment:', process.env.NODE_ENV);
```

### 2. 🔥 Firebase 連接問題

#### 問題：Firebase 功能異常
**症狀：**
- 登入功能失效
- Realtime Database 無法讀取資料
- Firestore 權限錯誤

**排查步驟：**
1. 檢查 Firebase Console 中的專案設定
2. 確認 Firestore 安全規則
3. 檢查 Realtime Database 權限設定
4. 驗證 Firebase SDK 版本相容性

**解決方案：**
```javascript
// 檢查 Firebase 初始化狀態
import firebase from 'firebase/app';
console.log('Firebase apps:', firebase.apps);
```

### 3. 🗺️ 地圖功能問題

#### 問題：Leaflet 地圖無法載入
**症狀：**
- 地圖區域顯示空白
- 地圖圖層載入失敗
- 座標定位不準確

**排查步驟：**
1. 檢查 Leaflet CSS 是否正確載入
2. 確認地圖 API Key 是否有效
3. 檢查網路連接到地圖服務提供商
4. 驗證座標資料格式

**解決方案：**
```javascript
// 在地圖組件中添加錯誤處理
map.on('tileerror', function(error) {
    console.error('Map tile error:', error);
});
```

### 4. 📄 PDF 生成問題

#### 問題：PDF 匯出功能失效
**症狀：**
- PDF 生成按鈕無反應
- PDF 檔案內容空白
- 下載失敗

**排查步驟：**
1. 檢查 jsPDF 和 html2canvas 版本
2. 確認瀏覽器支援 Canvas API
3. 檢查 PDF 生成的 DOM 元素
4. 驗證檔案下載權限

**解決方案：**
```javascript
// 添加錯誤處理
try {
    const canvas = await html2canvas(element);
    const pdf = new jsPDF();
    // ... PDF 生成邏輯
} catch (error) {
    console.error('PDF generation error:', error);
}
```

### 5. 🔐 Google OAuth 登入問題

#### 問題：Google 登入失敗
**症狀：**
- 登入按鈕無反應
- OAuth 彈窗被阻擋
- 登入後無法獲取用戶資訊

**排查步驟：**
1. 檢查 Google Client ID 是否正確
2. 確認網域是否已加入 OAuth 設定
3. 檢查瀏覽器彈窗阻擋設定
4. 驗證 OAuth 回調 URL

**解決方案：**
```javascript
// 檢查 Google OAuth 配置
console.log('Google Client ID:', process.env.REACT_APP_GOOGLE_CLIENT_ID);
```

---

## 🛠️ 最佳實踐

### 📦 部署前準備

#### 1. 代碼品質檢查
```bash
# 執行 ESLint 檢查
npm run lint

# 修復可自動修復的問題
npm run lint:fix

# 檢查 TypeScript 錯誤（如適用）
npx tsc --noEmit
```

#### 2. 依賴安全檢查
```bash
# 檢查安全漏洞
npm audit

# 修復安全問題
npm audit fix

# 檢查過時的依賴
npm outdated
```

#### 3. 建置優化
```bash
# 分析建置大小
npm run build
npx webpack-bundle-analyzer build/static/js/*.js
```

### 🔄 部署流程優化

#### 1. 自動化腳本範例
```bash
#!/bin/bash
# deploy.sh

echo "🚀 開始部署流程..."

# 檢查 Git 狀態
if [[ -n $(git status --porcelain) ]]; then
    echo "❌ 有未提交的變更，請先提交"
    exit 1
fi

# 切換到 main 分支並合併
git checkout main
git pull origin main
git merge develop

# 建置專案
echo "📦 建置專案..."
npm run build

# 檢查建置結果
if [ ! -d "build" ]; then
    echo "❌ 建置失敗"
    exit 1
fi

echo "✅ 部署準備完成"
```

#### 2. 環境變數驗證
```javascript
// scripts/validate-env.js
const requiredEnvVars = [
    'REACT_APP_API_NODE',
    'REACT_APP_ASK_GPT_API_NODE',
    'REACT_APP_GPT_API_NODE',
    'REACT_APP_GOOGLE_CLIENT_ID'
];

requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
        console.error(`❌ 缺少環境變數: ${envVar}`);
        process.exit(1);
    }
});

console.log('✅ 環境變數檢查通過');
```

### 📊 監控和日誌

#### 1. 前端錯誤監控
```javascript
// src/utils/errorHandler.js
window.addEventListener('error', (event) => {
    console.error('Global error:', event.error);
    // 發送錯誤到監控服務
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);
    // 發送錯誤到監控服務
});
```

#### 2. API 請求監控
```javascript
// src/api/interceptors.js
axios.interceptors.response.use(
    response => response,
    error => {
        console.error('API Error:', {
            url: error.config?.url,
            status: error.response?.status,
            message: error.message
        });
        return Promise.reject(error);
    }
);
```

---

## 📚 參考資源

### 🔗 相關文檔連結
- [React 部署指南](https://create-react-app.dev/docs/deployment/)
- [Firebase 文檔](https://firebase.google.com/docs)
- [Nginx 配置參考](https://nginx.org/en/docs/)

### 🛠️ 有用的工具
- **建置分析：** `webpack-bundle-analyzer`
- **效能監控：** Chrome DevTools Lighthouse
- **錯誤追蹤：** Sentry（建議）
- **API 測試：** Postman 或 Insomnia

### 📞 技術支援聯絡
- **前端技術：** [負責人] - [聯絡方式]
- **後端 API：** [負責人] - [聯絡方式]
- **伺服器維護：** [負責人] - [聯絡方式]
- **Firebase 管理：** [負責人] - [聯絡方式]

---

**知識庫維護：**
- 每次遇到新問題時，請更新此文檔
- 定期檢查解決方案的有效性
- 歡迎團隊成員貢獻最佳實踐

**最後更新：** [日期] by [更新人員]
