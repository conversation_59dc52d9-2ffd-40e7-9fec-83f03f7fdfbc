import axios from 'axios';
import { Base64 } from 'js-base64';

// configs
import { multiType } from './inputType';
import { Api } from '../../../api/Api';

export const createEntry = (graph, srcId, classtype, valueObj) => ({
    entry: {
        graph: `${graph}`,
        classType: `${classtype}`,
        srcId: `${srcId}`,
        value: valueObj
    }
});

const findPrefix = classType => {
    let prefix;
    switch (classType) {
        case 'AcademicDegree':
            prefix = 'ADG';
            break;
        case 'AcademicDiscipline':
            prefix = 'ADP';
            break;
        case 'Person':
            prefix = 'PER';
            break;
        case 'Publication':
            prefix = 'PUB';
            break;
        case 'Place':
            prefix = 'PLA';
            break;
        case 'Organization':
            prefix = 'ORG';
            break;
        default:
            break;
    }
    return prefix;
};

const getClassId = async (classType, keyword, graph) => {
    try {
        const response = await axios.get(
            Api.getClassKeywordId(Base64.encode(classType), Base64.encode(keyword), Base64.encode(graph))
        );
        if (response.data.data.length !== 0) {
            const { data } = response.data;
            return data[0].value;
        }
        return '';
    } catch (error) {
        console.error('Error fetching class ID:', error);
        return '';
    }
};

const postEntryInDb = async entry => {
    try {
        const response = await axios.post(Api.postGptReview, entry, {
            headers: { 'Content-Type': 'application/json' }
        });
        console.log('Entry posted successfully:', response.data);
    } catch (error) {
        console.error('Error posting entry:', error);
    }
};

const postToDB = async totalEntry => {
    const promises = totalEntry.map(async (entryItem, index) => {
        try {
            const response = await axios.post(Api.postGptReview, entryItem);
            console.log(`第 ${index} 條上傳成功:`, response.data.data, entryItem);
            return response.data;
        } catch (error) {
            console.log(`上傳第 ${index} 條失敗:`, error);
            return error;
        }
    });

    const results = await Promise.allSettled(promises);
    console.log('全部請求結果:', results);
};

export const deleteGraph = async graph => {
    try {
        const entry = {
            entry: {
                graph: `${graph}`
            }
        };

        await axios.delete(Api.deleteGraph, { data: entry });
    } catch (e) {
        console.log('delete graph fail：', e);
    }
};

export const postEntry = async (inputText, fetchData, dataset, user, status, graph, id, time) => {
    if (!inputText || fetchData.length === 0 || Object.keys(dataset).length === 0 || Object.keys(user).length === 0)
        return;

    let srcId;
    let personName;
    const totalEntry = [];

    fetchData.forEach(item => {
        const { classType, sub: items, titleEn } = item;
        if (titleEn === 'BasicInformation') {
            // step1: find srcId
            Object.entries(...items).forEach(key => {
                const [property, valueObj] = key;
                if (property === 'bestKnownName') {
                    if (valueObj.value) {
                        srcId = `PER${Base64.encode(encodeURI(valueObj.value))}`;
                        personName = valueObj.value;
                    }
                }
            });
        }

        if (srcId) {
            // step2: filter empty data
            const newDatas = items
                .map(i => i)
                .map(i => {
                    const newObj = {};
                    Object.entries(i).forEach(data => {
                        const [property, valueObj] = data;
                        if (valueObj.value) {
                            newObj[property] = valueObj.value;
                        }
                    });
                    return newObj;
                });

            newDatas.forEach(nds => {
                if (Object.keys(nds).length !== 0) {
                    const entryValue = {};
                    Object.entries(nds).forEach(async key => {
                        const [property, value] = key;
                        const isMultiType = Boolean(multiType.includes(property));
                        const isCoordsType = property.endsWith('Place') || property.endsWith('Organization');

                        // step3 find range and create range Entry
                        const index = property.lastIndexOf('_');
                        const range = index > 0 ? property.slice(index + 1) : property;

                        if (range === 'bestKnownName') {
                            entryValue.originalText__string = inputText.replace(/(\r\n|\n|\r)/gm, '');
                            entryValue[property] = [value];
                        } else if (['DateEvent', 'string'].includes(range)) {
                            entryValue[property] = value;
                        } else if (range === 'Publication') {
                            // range = Publication 都要先建立並且產生id
                            const rangeValue = { [`label_${range}`]: [value] };
                            const specialEntry = createEntry(graph, 'not__needed', range, rangeValue);
                            // 這邊一定要先post 才可以回頭抓Id
                            postEntryInDb(specialEntry);

                            // 拿到id
                            const classKeywordId = await getClassId(range, value, graph);
                            entryValue[property] = [`${classKeywordId}`];
                        } else {
                            const rangePrfix = findPrefix(range);
                            // for multi data
                            if (isMultiType || isCoordsType) {
                                value.split('\n').forEach(data => {
                                    const rangeSrcId = `${rangePrfix}${Base64.encode(encodeURI(data))}`;
                                    const rangeValue = {
                                        ...(['Person', 'Organization', 'Place'].includes(range)
                                            ? {
                                                  ...(range === 'Person' && { bestKnownName: [data] }),
                                                  ...(range === 'Organization' && { bestKnownName: [data] }),
                                                  ...(range === 'Place' && { [`label_${range}`]: [data] })
                                              }
                                            : { [`label_${range}`]: [data] })
                                    };

                                    entryValue[property] = entryValue[property]
                                        ? entryValue[property].concat([`${rangePrfix}${encodeURI(data)}`])
                                        : [`${rangePrfix}${encodeURI(data)}`];

                                    const rangeEntry = createEntry(graph, rangeSrcId, range, rangeValue);
                                    totalEntry.push(rangeEntry);
                                });
                            } else {
                                const rangeSrcId = `${rangePrfix}${Base64.encode(encodeURI(value))}`;

                                const rangeValue =
                                    range === 'Person' ? { bestKnownName: [value] } : { [`label_${range}`]: [value] };

                                const rangeEntry = createEntry(graph, rangeSrcId, range, rangeValue);

                                entryValue[property] = `${rangePrfix}${encodeURI(value)}`;
                                totalEntry.push(rangeEntry);
                            }
                        }
                    });

                    const result = createEntry(graph, srcId, classType, entryValue);

                    totalEntry.push(result);
                }
            });
        }
    });

    const nerEntryValue = {
        nerTime: time,
        nerUser: user.displayName,
        nerPerson: personName,
        nerID: id,
        nerDataset: dataset.id,
        nerStatus: status || 'waiting',
        nerMail: user.email
    };

    const nerResult = createEntry(graph, srcId, 'NER', nerEntryValue);

    totalEntry.push(nerResult);

    postToDB(totalEntry);
};
