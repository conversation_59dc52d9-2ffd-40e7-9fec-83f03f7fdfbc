import React, { useEffect, useState } from 'react';
import '../Auth.scss';
import { confirmPasswordReset, verifyPasswordResetCode } from '@firebase/auth';
import { CircularProgress } from '@mui/material';
import { getAuth } from 'firebase/auth';
import { z } from 'zod';
import { isEmpty } from '../../../common/codes';
import hidePasswordIcon from '../../../images/login_page/hide_password_icon.svg';
import loginPageImg from '../../../images/login_page/login_page_image.jpg';
import showPasswordIcon from '../../../images/login_page/show_password_icon.svg';

const ResetPassword = ({ oobCode }) => {
    const [newPassword, setNewPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState(null);
    const [message, setMessage] = useState(null);
    const [loading, setLoading] = useState(true);
    const [email, setEmail] = useState('');
    const auth = getAuth();
    console.log('error', error, message, email);

    const togglePasswordVisibility = () => {
        setShowPassword(prevShowPassword => !prevShowPassword);
    };

    useEffect(() => {
        let redirectTimer;

        const checkAuthState = () => {
            const currentUser = auth?.currentUser;

            if (!isEmpty(currentUser)) {
                setMessage('您已登入，即將返回首頁...');
                redirectTimer = setTimeout(() => {
                    window.location.href = '/';
                }, 3000);
            }

            setLoading(false);
        };

        // 監聽 auth 狀態變化
        const unsubscribe = auth.onAuthStateChanged(() => {
            checkAuthState();
        });

        return () => {
            unsubscribe();
            if (redirectTimer) clearTimeout(redirectTimer);
        };
    }, [auth]);

    useEffect(() => {
        const getUserEmail = async () => {
            try {
                const tmpEmail = await verifyPasswordResetCode(auth, oobCode);
                setEmail(tmpEmail);
                // eslint-disable-next-line no-shadow
            } catch (error) {
                console.error('重設密碼錯誤:', error);
            }
        };

        getUserEmail();
    });

    const passwordSchema = z
        .string()
        .min(8, '密碼至少需要 8 個字符')
        .regex(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/,
            '密碼必須包含至少一個大寫字母、一個小寫字母和一個數字'
        );

    const handleResetPassword = async e => {
        e.preventDefault();
        setError(null);

        try {
            passwordSchema.parse(newPassword);
        } catch (validationError) {
            setError(validationError.errors[0].message);
            return;
        }

        try {
            await verifyPasswordResetCode(auth, oobCode);
            await confirmPasswordReset(auth, oobCode, newPassword);
            setMessage('密碼已成功重設。');
            // eslint-disable-next-line no-shadow
        } catch (error) {
            console.error('重設密碼錯誤:', error);
            setError('重設密碼失敗，請稍後再試。');
        }
    };

    if (loading) {
        return (
            <div className="reset-password-form">
                <CircularProgress sx={{ display: 'flex', margin: 'auto' }} />
            </div>
        );
    }

    return (
        <div className="auth">
            <div className="authContainer">
                <div className="authImg">
                    <img src={loginPageImg} alt="signIn_page_image" />
                </div>
                <div className="authContent">
                    <h1 className="authTitle">登入</h1>
                    <p>重新設定帳戶email...的登入密碼。</p>
                    <div className="authForm">
                        <form onSubmit={handleResetPassword} className="sign-in-form">
                            <div className="authField">
                                <p htmlFor="password" className="authLabel">
                                    新密碼
                                    <span className="authRequired"> *</span>
                                </p>
                                <div className="passwordInputContainer">
                                    <input
                                        type={showPassword ? 'text' : 'password'}
                                        value={newPassword}
                                        onChange={e => setNewPassword(e.target.value)}
                                        placeholder="Password"
                                        className="authInput"
                                        required
                                        name="password"
                                        id="password"
                                        autoComplete="off"
                                    />
                                    <button
                                        type="button"
                                        className="togglePasswordButton"
                                        onClick={togglePasswordVisibility}
                                        aria-label={showPassword ? '隱藏密碼' : '顯示密碼'}
                                    >
                                        <img
                                            src={showPassword ? hidePasswordIcon : showPasswordIcon}
                                            alt={showPassword ? '隱藏密碼' : '顯示密碼'}
                                            className="passwordIcon"
                                        />
                                    </button>
                                </div>
                            </div>
                            <button className="authButton authButton--signIn" type="submit">
                                重置密碼
                            </button>
                        </form>
                    </div>
                    {/* <SignInForm */}
                    {/*   account={account} */}
                    {/*   handleAccountChange={handleAccountChange} */}
                    {/*   setIsSignUp={setIsSignUp} */}
                    {/* /> */}
                </div>
            </div>
        </div>
    );
};

export default ResetPassword;
