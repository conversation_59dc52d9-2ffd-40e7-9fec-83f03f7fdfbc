import axios from 'axios';

const LOCALE_LANG = {
    LOCALE_DEFAULT: 'zh-hans'
};

let baseUrl;
let baseAskGptUrl;
let baseGptUrl;
let baseGptReviewUrl;

if (process.env.NODE_ENV === 'production') {
    baseUrl = process.env.REACT_APP_API_NODE;
    baseAskGptUrl = process.env.REACT_APP_ASK_GPT_API_NODE;
    baseGptUrl = process.env.REACT_APP_GPT_API_NODE;
    baseGptReviewUrl = process.env.REACT_APP_GPT_REVIEW_API_NODE;
} else {
    baseUrl = process.env.REACT_APP_API_NODE;
    baseAskGptUrl = process.env.REACT_APP_ASK_GPT_API_NODE;
    baseGptUrl = process.env.REACT_APP_GPT_API_NODE;
    baseGptReviewUrl = process.env.REACT_APP_GPT_REVIEW_API_NODE;
}

const locale = LOCALE_LANG.LOCALE_DEFAULT;

export const Api = {
    getAxios: () => axios,
    setAxiosAuth(token) {
        // 不加 prefix "Bearer "
        axios.defaults.headers.Authorization = `${token}`;
        axios.defaults.headers.common.Authorization = `${token}`;
    },
    restfulHKBDB: `${baseUrl}/${locale}/generic/2.0`,
    deleteEvent: `${baseUrl}/${locale}/deleteEvent/1.0`,

    postGptReview: `${baseGptReviewUrl}/${locale}/generic/2.0`,
    getOriginalTextApi: (author, graph) =>
        `${baseGptReviewUrl}/${locale}/person/information/1.0?limit=-1&offset=0&name=${author}&graph=${graph}`,
    getClassKeywordId: (classType, keyword, graph) =>
        `${baseGptReviewUrl}/${locale}/class/findId/1.0?limit=-1&offset=0&class=${classType}&keyword=${keyword}&graph=${graph}`,
    getAllEventIds: (name, graph, classtype) =>
        `${baseGptReviewUrl}/${locale}/person/information/${classtype}/eventids/1.0?limit=-1&offset=0&name=${name}&graph=${graph}`,
    getPublicationEvtIds: graph =>
        `${baseGptReviewUrl}/${locale}/person/information/publication/eventids/1.1?limit=-1&offset=0&graph=${graph}`,
    getArticleEvtIds: graph =>
        `${baseGptReviewUrl}/${locale}/person/information/article/eventids/1.1?limit=-1&offset=0&graph=${graph}`,
    getOtherWorkEvtIds: graph =>
        `${baseGptReviewUrl}/${locale}/person/information/otherwork/eventids/1.1?limit=-1&offset=0&graph=${graph}`,
    getAllEvent: (name, graph, classtype, eventId) =>
        `${baseGptReviewUrl}/${locale}/person/information/${classtype}/1.0?limit=-1&offset=0&name=${name}&eventids=${eventId}&graph=${graph}`,
    getPublicationEvt: (graph, eventId) =>
        `${baseGptReviewUrl}/${locale}/person/information/publication/1.1?limit=-1&offset=0&eventids=${eventId}&graph=${graph}`,
    getArticleEvt: (graph, eventId) =>
        `${baseGptReviewUrl}/${locale}/person/information/article/1.1?limit=-1&offset=0&eventids=${eventId}&graph=${graph}`,
    getOtherWorkEvt: (graph, eventId) =>
        `${baseGptReviewUrl}/${locale}/person/information/otherwork/1.1?limit=-1&offset=0&eventids=${eventId}&graph=${graph}`,
    getPersonInformation: (name, graph) =>
        `${baseGptReviewUrl}/${locale}/person/information/1.0?limit=-1&offset=0&name=${name}&graph=${graph}`,
    getPersonNameNode: (classtype, eventId, graph) =>
        `${baseGptReviewUrl}/${locale}/person/information/${classtype}/1.0?limit=-1&offset=0&eventids=${eventId}&graph=${graph}`,
    getCommentsUrl: graph => `${baseGptReviewUrl}/${locale}/comment/list/1.0?limit=-1&offset=0&graph=${graph}`,

    getGptBaseInfo: `${baseAskGptUrl}/baseinfo`,
    getGptNameInfo: `${baseAskGptUrl}/nameinfo`,
    getGptEducate: `${baseAskGptUrl}/educate`,
    getGptWork: `${baseAskGptUrl}/work`,
    getGptPublication: `${baseAskGptUrl}/publication`,
    getGptArticle: `${baseAskGptUrl}/article`,
    getGptEvent: `${baseAskGptUrl}/event`,
    getGptHonor: `${baseAskGptUrl}/honor`,
    getGptOrganize: `${baseAskGptUrl}/organize`,
    getGptPiece: `${baseAskGptUrl}/piece`,
    getGptRelation: `${baseAskGptUrl}/relation`,
    getGptReviewSection: `${baseAskGptUrl}/review_section`,

    getDatasetList: `${baseGptUrl}/${locale}/gpt/dataset/list/1.0?limit=-1&offset=0`,

    deleteGraph: `${baseGptReviewUrl}/${locale}/deleteGraph/1.0`,

    // work area
    getUserReviewData: (tabId, mail) =>
        `${baseGptReviewUrl}/${locale}/user/${tabId}/list/1.0?limit=-1&offset=0&mail=${mail}`,

    getWriterList: `${baseGptUrl}/${locale}/gpt/writer/list/1.1?limit=-1&offset=0`,
    getFindSimilar: `${baseGptUrl}/${locale}/gpt/find/similar/1.1?limit=-1&offset=0&query1={query1}&query2={query2}&query3={query3}`,

    // Coordinates
    getCoordinates: type => `${baseUrl}/${locale}/get/${type}/coordinates/1.0?limit=-1&offset=0`,
    getGoogleCoord: `${baseUrl}/gis`
};

export const addAxiosInterceptor = (_axios, _millisToSS, _millisToMMSS) => {
    const addDurationAttr = (obj, duration) => {
        // eslint-disable-next-line no-param-reassign
        if (_millisToSS) obj.durationSS = _millisToSS(duration);
        // eslint-disable-next-line no-param-reassign
        if (_millisToMMSS) obj.durationMMSS = _millisToMMSS(duration);
        return obj;
    };

    // Add a request interceptor
    _axios.interceptors.request.use(
        config => {
            // Do something before request is sent
            // eslint-disable-next-line no-param-reassign
            config.metadata = { startTime: new Date() };
            return config;
        },
        error =>
            // Do something with request error
            Promise.reject(error)
    );

    // Add a response interceptor
    _axios.interceptors.response.use(
        response => {
            // Any status code that lie within the range of 2xx cause this function to trigger
            // Do something with response data
            response.config.metadata.endTime = new Date();
            response.duration = response.config.metadata.endTime - response.config.metadata.startTime;
            return addDurationAttr(response, response.duration);
        },
        error => {
            // Any status codes that falls outside the range of 2xx cause this function to trigger
            // Do something with response error
            // eslint-disable-next-line no-param-reassign
            error.config.metadata.endTime = new Date();
            // eslint-disable-next-line no-param-reassign
            error.duration = error.config.metadata.endTime - error.config.metadata.startTime;
            return addDurationAttr(error, error.duration);
        }
    );
};

export default { Api };
