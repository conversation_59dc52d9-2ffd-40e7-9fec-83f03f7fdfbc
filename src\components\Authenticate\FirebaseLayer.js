// firebase
import { getAuth } from 'firebase/auth';
import firebase from 'firebase/compat/app';
// import "firebase/database";
// import "firebase/firestore";
// import "firebase/analytics";

import AuthListener from './firebase/AuthListener';
// import RealTimeListener from "./firebase/RealTimeListener";
// import StorageListener from "./firebase/StorageListener";
import firebaseConfig from '../../api/config/config-firebase';

// Initialize Firebase
firebase.initializeApp(firebaseConfig);
// firebase.getAnalytics(app);

const firebaseAuth = getAuth();

// eslint-disable-next-line react/prop-types
const FirebaseLayer = ({ children }) => {
    AuthListener({ firebaseAuth });
    return children || null;
};

export default FirebaseLayer;
