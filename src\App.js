import React from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { BrowserRouter } from 'react-router-dom';
import FirebaseLayer from './components/Authenticate/FirebaseLayer';
import Body from './pages/Body';
import Header from './pages/Header';
import StoreProvider from './store/StoreProvider';
import theme from './utils/theme';

const App = () => (
    <>
        {/* 使用 BrowserRouter 建立 history object */}
        <BrowserRouter>
            <GoogleOAuthProvider clientId={process.env.REACT_APP_GOOGLE_CLIENT_ID}>
                {/* 在最上層提供 global store */}
                <StoreProvider>
                    <FirebaseLayer>
                        <ThemeProvider theme={theme}>
                            {/* 載入資料相關區 */}
                            {/* <DataLayer /> */}
                            <div className="App">
                                {/* 頁面頂層連結區, 如要變更內容請至 APP-layout.js */}
                                <Header />
                                {/* 頁面跳轉處理區, 如要變更內容請至 APP-layout.js */}
                                <Body />
                                {/* <Footer /> */}
                            </div>
                        </ThemeProvider>
                    </FirebaseLayer>
                </StoreProvider>
            </GoogleOAuthProvider>
        </BrowserRouter>
    </>
);

export default App;
