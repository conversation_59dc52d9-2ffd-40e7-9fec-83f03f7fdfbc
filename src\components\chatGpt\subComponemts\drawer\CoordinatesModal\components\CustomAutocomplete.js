import React from 'react';
import { Autocomplete, TextField } from '@mui/material';

const MAX_OPTIONS_COUNT = 50;

const CustomAutocomplete = ({ value = '', options = [], onChange = () => {}, sx = {} }) => {
    const handleInputChange = (e, inputValue) => {
        onChange(inputValue);
    };

    return (
        <Autocomplete
            disableClearable
            freeSolo
            options={Array.isArray(options) ? options : []}
            filterOptions={(opts, state) => {
                const filtered = opts.filter(opt => opt.label.toLowerCase().includes(state.inputValue.toLowerCase()));
                return filtered.slice(0, MAX_OPTIONS_COUNT);
            }}
            inputValue={value}
            onInputChange={handleInputChange}
            getOptionLabel={option => (option.label ? option.label : String(option))}
            renderOption={(props, option) => (
                <li {...props} key={option.id}>
                    {option.label}
                </li>
            )}
            renderInput={params => (
                <TextField
                    {...params}
                    variant="standard"
                    InputProps={{
                        ...params.InputProps,
                        disableUnderline: true
                    }}
                    sx={sx}
                />
            )}
        />
    );
};

export default CustomAutocomplete;
