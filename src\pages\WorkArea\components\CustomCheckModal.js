/* eslint-disable react/forbid-prop-types */
import React, { useState } from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Modal from '@mui/material/Modal';
import Typography from '@mui/material/Typography';
import PropTypes from 'prop-types';

const modalStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: '#FFFFFF',
    borderRadius: '12px'
};

const actionButtonStyle = {
    padding: '8px 16px',
    bgcolor: '#FAFAFA',
    color: '#757575',
    borderRadius: '8px',
    '&:hover': {
        bgcolor: '#ededed'
    }
};

const CustomCheckModal = ({
    trigger = 'open',
    disabled = false,
    title = 'modal title',
    description = 'modal description',
    actionBtnText = '確認',
    actionBtnStyle,
    onClick = () => {}
}) => {
    const [open, setOpen] = useState(false);
    const handleOpen = () => setOpen(true);
    const handleClose = () => setOpen(false);

    return (
        <div>
            <Button disabled={disabled} sx={{ minWidth: '0px', padding: '8px' }} onClick={handleOpen}>
                {trigger}
            </Button>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={modalStyle}>
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'space-between',
                            alignItems: 'center',
                            padding: '18px 24px',
                            bgcolor: '#F4F8FB',
                            border: 'solid 1px #DAE9EF',
                            borderRadius: '12px 12px 0px 0px'
                        }}
                    >
                        <Typography sx={{ color: '#336F89', fontWeight: 600, fontSize: '20px' }} id="modal-modal-title">
                            {title}
                        </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', padding: '24px' }}>
                        <Typography sx={{ fontSize: '14px', fontWeight: 400 }} id="modal-modal-description">
                            {description}
                        </Typography>
                    </Box>
                    <Box
                        sx={{
                            display: 'flex',
                            justifyContent: 'end',
                            alignItems: 'center',
                            gap: '8px',
                            padding: '0px 24px 24px 24px'
                        }}
                    >
                        <Button sx={actionButtonStyle} onClick={handleClose}>
                            取消
                        </Button>
                        <Button
                            sx={{ ...actionButtonStyle, ...actionBtnStyle }}
                            onClick={() => {
                                onClick();
                                handleClose();
                            }}
                        >
                            {actionBtnText}
                        </Button>
                    </Box>
                </Box>
            </Modal>
        </div>
    );
};

CustomCheckModal.propTypes = {
    trigger: PropTypes.any,
    disabled: PropTypes.bool,
    title: PropTypes.string,
    description: PropTypes.string,
    actionBtnText: PropTypes.string,
    actionBtnStyle: PropTypes.object,
    onClick: PropTypes.func
};

export default CustomCheckModal;
