import { produce } from 'immer';

import Act from '../actions';

const initState = {
    registerUserInfo: {}
};

// eslint-disable-next-line consistent-return
const signUpReducer = produce((draft, action) => {
    switch (action?.type) {
        case Act.REGISTER_USER_INFO:
            // eslint-disable-next-line no-param-reassign
            draft.registerUserInfo = { ...draft.registerUserInfo, ...action.payload };
            break;
        default:
            return draft;
    }
}, initState);

export default signUpReducer;
