import { useContext, useEffect, useState } from 'react';
import axios from 'axios';
import { addAxiosInterceptor, Api } from '../api/Api';
import { millisToSS } from '../common/codes';
import { StoreContext } from '../store/StoreProvider';

const useTokenReady = () => {
    // store
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { locale } = user;
    // local state
    const [tokenReady, setTokenReady] = useState(false);
    const [axiosReady, setAxiosReady] = useState(false);

    // 是否為 production mode => production mode 追溯至 firebase 設定
    // eslint-disable-next-line no-unused-vars
    const isProductionMode = _production => _production && _production === 'true';

    // 驗證 axios headers 是否有足夠資訊
    // eslint-disable-next-line no-shadow
    const checkAxiosHeaders = axios =>
        (axios.defaults.headers.Authorization &&
            axios.defaults.headers.Authorization !== '' &&
            axios.defaults.headers.common.Authorization &&
            axios.defaults.headers.common.Authorization !== '') ||
        false;

    useEffect(() => {
        // 包裝 token 進 axios config 中,以便 fetch API 帶有 token
        // FIXME: 因 hkbdb-api 驗證使用者的 token 有時會 fail, 改用 自訂義 token
        Api.setAxiosAuth(process.env.REACT_APP_AUTH_TOKEN);
        // Api.setAxiosAuth(globalToken);
        // 改變 tokenReady 狀態
        setTokenReady(true);
        // add interceptor to global axios
        addAxiosInterceptor(axios, millisToSS);
        /**
         * set axios global config default
         * 在請求中加入語系, 加入 token
         * */
        axios.defaults.headers = {
            ...axios.defaults.headers,
            'Accept-Language': locale
            // 'Cache-Control': 'no-cache',
        };

        if (checkAxiosHeaders(axios)) setAxiosReady(true);
    }, []);

    return { tokenReady, axiosReady };
};

export default useTokenReady;
