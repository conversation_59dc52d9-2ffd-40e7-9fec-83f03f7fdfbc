const titleType = [
    {
        id: 0,
        titleEn: 'BasicInformation',
        titleAbbreviation: 'PER',
        classType: 'Person',
        littleClassType: 'person'
    },
    {
        id: 1,
        titleEn: 'NameInformation',
        titleAbbreviation: 'NNI',
        classType: 'NameNode',
        littleClassType: 'namenode'
    },
    {
        id: 2,
        titleEn: 'Education',
        titleAbbreviation: 'EDUEVT',
        classType: 'EducationEvent',
        littleClassType: 'education'
    },
    {
        id: 3,
        titleEn: 'Work',
        titleAbbreviation: 'EMPEVT',
        classType: 'EmploymentEvent',
        littleClassType: 'employment'
    },
    {
        id: 4,
        titleEn: 'Publication',
        titleAbbreviation: 'PUB',
        classType: 'Publication',
        littleClassType: 'publication'
    },
    {
        id: 5,
        titleEn: 'Article',
        titleAbbreviation: 'ART',
        classType: 'Article',
        littleClassType: 'article'
    },
    {
        id: 6,
        titleEn: 'Pieces',
        titleAbbreviation: 'OTW',
        classType: 'OtherWork',
        littleClassType: 'otherwork'
    },
    {
        id: 7,
        titleEn: 'RelatedOrganizations',
        titleAbbreviation: 'ORGEVT',
        classType: 'organizationevent',
        littleClassType: 'organization'
    },
    {
        id: 8,
        titleEn: 'Connections',
        titleAbbreviation: 'RELEVT',
        classType: 'relationevent',
        littleClassType: 'relationevent'
    },
    {
        id: 9,
        titleEn: 'RelativeEvent',
        titleAbbreviation: 'EVT',
        classType: 'Event',
        littleClassType: 'event'
    },
    {
        id: 10,
        titleEn: 'Honor',
        titleAbbreviation: 'AWEEVT',
        classType: 'awardevent',
        littleClassType: 'award'
    },
    {
        id: 11,
        titleEn: 'ReviewSection',
        titleAbbreviation: 'RVEVT',
        classType: 'ReviewEvent',
        littleClassType: 'review'
    }
];

export default titleType;
