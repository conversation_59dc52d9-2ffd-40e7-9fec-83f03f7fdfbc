import React, { useContext, useEffect, useState } from 'react';

// firebase
import { signOut, getAuth } from 'firebase/auth';
import { Redirect } from 'react-router-dom';

// store
import itemConfig from '../../api/config/config-localStorage';
import act from '../../store/actions';
import { StoreContext } from '../../store/StoreProvider';

const SignOutPage = () => {
    const isLogin = JSON.parse(localStorage.getItem(itemConfig.isLogin));

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const [_, setUserName] = useState('');

    useEffect(() => {
        if (isLogin) {
            // console.log("console.log from SignOut Page:", isLogin);
            const auth = getAuth();
            // SignOut firebase
            signOut(auth)
                .then(() => {
                    setUserName(user.displayName);
                    // Sign-out successful.
                })
                .catch(error => {
                    // An error happened.
                    console.log(error);
                });
            // clean user data
            dispatch({ type: act.FIREBASE_LOGOUT_USER });
            // clean localStorage
            localStorage.removeItem(itemConfig.isLogin);
        } else {
            // console.log('do nothing');
        }
    });

    // return <h1 style={{ textAlign: "center" }}>SignOut already!</h1>;
    // return <Redirect to="SignIn" />;
    // 登出後導回首頁
    return <Redirect to="/" />;
};

export default SignOutPage;
