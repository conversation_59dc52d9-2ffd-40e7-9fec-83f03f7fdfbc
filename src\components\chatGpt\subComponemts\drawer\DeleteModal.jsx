import React from 'react';
import { Box, But<PERSON>, Modal } from '@mui/material';
import Typography from '@mui/material/Typography';

const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 480,
    bgcolor: '#FFFFFF',
    p: 4,
    borderRadius: '8px',
    display: 'flex',
    flexDirection: 'column',
    padding: 0
};

const titleDivStyle = {
    bgcolor: '#F4F8FB',
    padding: '16px 24px',
    color: '#336F89',
    borderRadius: 2,
    '& h2': {
        fontWeight: 600
    }
};

const contentDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '40px 24px',
    borderRadius: 2,
    '& p': {
        fontSize: '14px',
        textAlign: 'center'
    },
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
};

const buttonDivStyle = {
    bgcolor: '#FFFFFF',
    padding: '0 24px 16px 24px',
    '& p': {
        fontSize: '14px'
    },
    display: 'flex',
    justifyContent: 'flex-end',
    gap: '0.5rem'
};

const cancelButtonStyle = {
    padding: ' 8px 16px',
    backgroundColor: '#FAFAFA',
    color: '#757575',
    borderRadius: '8px'
};

const deleteButtonStyle = {
    padding: ' 8px 16px',
    '&:hover': {
        backgroundColor: `#E2533F`
    },
    backgroundColor: '#D14835',
    color: '#FFFFFF',
    borderRadius: '8px'
};

const DeleteModal = ({ open, setOpen, deleteFct }) => {
    const handleClose = () => {
        setOpen(false);
    };

    return (
        <div>
            <Modal
                open={open}
                onClose={handleClose}
                aria-labelledby="modal-modal-title"
                aria-describedby="modal-modal-description"
            >
                <Box sx={style}>
                    <Box sx={titleDivStyle}>
                        <Typography id="modal-modal-title" variant="h6" component="h2">
                            刪除資料
                        </Typography>
                    </Box>
                    <Box sx={contentDivStyle}>
                        <Typography id="modal-modal-description">是否確定刪除該筆資料?</Typography>
                    </Box>
                    <Box sx={buttonDivStyle}>
                        <Button sx={cancelButtonStyle} onClick={handleClose}>
                            取消
                        </Button>
                        <Button
                            sx={deleteButtonStyle}
                            onClick={() => {
                                deleteFct();
                                handleClose();
                            }}
                        >
                            刪除
                        </Button>
                    </Box>
                </Box>
            </Modal>
        </div>
    );
};

export default DeleteModal;
