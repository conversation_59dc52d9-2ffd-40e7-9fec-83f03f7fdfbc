import axios from 'axios';
import { Base64 } from 'js-base64';
import { Api } from '../../../../../api/Api';
import { createEntry as makeCreateEntry } from '../../../utils/entry';

export const getCoordinates = async (type = 'all') => {
    const res = await axios.get(Api.getCoordinates(type));
    return res.data.data;
};

export const createDataToDB = async (url, entry) => {
    try {
        await axios.post(url, entry);
    } catch (e) {
        console.log('createDataToDB failed: ', e, url);
    }
};

export const updateDataInDB = async (url, entry) => {
    try {
        await axios.put(url, entry);
    } catch (e) {
        console.log('updateDataInDB failed: ', e, url);
    }
};

export const makeUpdateEntry = (graph, srcId, classType, srcValue, dstValue) => ({
    entryDst: {
        graph,
        srcId,
        classType,
        value: dstValue
    },
    entrySrc: {
        graph,
        srcId,
        classType,
        value: srcValue
    }
});

const EDIT_COORD_TYPES = ['Place', 'Organization'];

const encodeLocationId = id => {
    if (!id.startsWith('PLA') && !id.startsWith('ORG')) return null;

    const prefix = id.startsWith('PLA') ? 'PLA' : 'ORG';
    const baseEncodeName = Base64.encode(id.replace(/^PLA|^ORG/, ''));
    return `${prefix}${baseEncodeName}`;
};

export const saveCoordsChange = async (coords, type) => {
    if (!type || !EDIT_COORD_TYPES.includes(type) || !Array.isArray(coords) || !coords.length) return;

    const graph = 'geo';
    const url = Api.restfulHKBDB;

    // eslint-disable-next-line no-restricted-syntax
    for (const coord of coords) {
        const id =
            coord.status === 'create'
                ? `${type === 'Place' ? 'PLA' : 'ORG'}${Base64.encode(encodeURI(coord.new.label))}`
                : encodeLocationId(coord.new.id);

        if (id) {
            const newValue = {
                [`label_${type}`]: coord.new.label,
                geoLatitude: coord.new.latitude,
                geoLongitude: coord.new.longitude
            };

            const oldValue = {
                [`label_${type}`]: coord.old.label,
                geoLatitude: coord.old.latitude,
                geoLongitude: coord.old.longitude
            };

            switch (coord.status) {
                case 'create': {
                    const entry = makeCreateEntry(graph, id, type, newValue);

                    // eslint-disable-next-line no-await-in-loop
                    await createDataToDB(url, entry);
                    break;
                }
                case 'update': {
                    const entry = makeUpdateEntry(graph, id, type, oldValue, newValue);

                    // eslint-disable-next-line no-await-in-loop
                    await updateDataInDB(url, entry);
                    break;
                }
                default:
                    break;
            }
        }
    }
};

export const getGoogleCoord = async location => {
    const defaultCoord = { id: '', label: location, longitude: '', latitude: '' };

    try {
        const entry = { address: location };
        const res = await axios.post(Api.getGoogleCoord, entry);

        return res?.data ? { ...defaultCoord, longitude: res.data.lng, latitude: res.data.lat } : defaultCoord;
    } catch (e) {
        console.log('getGoogleCoord failed: ', e);
        return defaultCoord;
    }
};
