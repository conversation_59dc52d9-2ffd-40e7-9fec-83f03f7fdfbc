// auth
import authority from './App-authority';
// HTML Header: Nav Bar
// authority: 使用者是否可以進入該頁面由 authority 控制
// public: 開發中的頁面, public 設為 false

const pathConfig = {
    root: {
        url: '/',
        label: 'Home'
    },
    workArea: {
        url: '/WorkArea',
        label: '我的工作區'
    },
    signIn: {
        url: '/SignIn',
        label: '登入'
    },
    signOut: {
        url: '/SignOut',
        label: '登出'
    }
};

export const menus = {
    menuLeft: [
        // {
        //   id: "menu-left-01",
        //   name: pathConfig.question.label,
        //   path: pathConfig.question.url,
        //   authority: authority.Question,
        // }
    ],
    menuRight: [
        // showOnLogin: user login 時，是否要顯示
        {
            id: 'menu-right-01',
            name: pathConfig.signIn.label,
            path: pathConfig.signIn.url,
            authority: authority.SignIn
        },
        {
            id: 'menu-right-02',
            name: pathConfig.signOut.label,
            path: pathConfig.signOut.url,
            authority: authority.SignOut,
            showOnLogin: true
        }
    ]
};

/**
 * @currentAuthority: String
 * @authority: Array
 * */
// eslint-disable-next-line no-shadow
export const isPermitting = (currentAuthority, authority) => authority.includes(currentAuthority);
