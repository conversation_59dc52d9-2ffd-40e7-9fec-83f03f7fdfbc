const defaultEmptyData = [
    {
        id: 0,
        title: '基本資料',
        titleEn: 'BasicInformation',
        classType: 'Person',
        sub: [
            {
                bestKnownName: { title: '常見名稱', value: '' },
                deathAge__string: { title: '辭世年齡', value: '' },
                deathCause__string: { title: '死因', value: '' },
                disable__string: { title: '殘廢', value: '' },
                displayRetireDate__string: { title: '退休年', value: '' },
                gender__string: { title: '性別', value: '' },
                hasBirthDate__DateEvent: { title: '出生日期', value: '' },
                hasDeathDate__DateEvent: { title: '逝世日期', value: '' },
                hasNationality__Place: { title: '國籍', value: '' },
                hasNativePlace__Place: { title: '籍貫', value: '' },
                hasPlaceOfBirth__Place: { title: '出生地點', value: '' },
                hasPlaceOfBuried__Place: { title: '埋葬地點', value: '' },
                hasPlaceOfDeath__Place: { title: '逝世地點', value: '' },
                illness__string: { title: '疾病', value: '' },
                occupation__string: { title: '社會身份', value: '' },
                reputation__string: { title: '聲譽', value: '' },
                researchArea__string: { title: '研究領域', value: '' },
                // originalName__string: { title: "本名", value: "" },
                // zi__string: { title: "字", value: "" },
                // hao__string: { title: "號", value: "" },
                // penName__string: { title: "筆名", value: "" },
                // joinPenName__string: { title: "共同筆名", value: "" },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 1,
        title: '人名資料',
        titleEn: 'NameInformation',
        classType: 'NameNode',
        sub: [
            {
                nnBestKnownName__string: { title: '常見名稱', value: '' },
                originalName__string: { title: '本名', value: '' },
                zi__string: { title: '字', value: '' },
                hao__string: { title: '號', value: '' },
                penName__string: { title: '筆名', value: '' },
                joinPenName__string: { title: '共同筆名', value: '' },
                name__string: { title: '其他名稱', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 2,
        title: '學歷',
        titleEn: 'Education',
        classType: 'EducationEvent',
        sub: [
            {
                desc__string: { title: '描述', value: '' },
                hasAcademicDegree__AcademicDegree: { title: '學歷', value: '' },
                hasAcademicDiscipline__AcademicDiscipline: {
                    title: '學系',
                    value: ''
                },
                hasEducatedAt__Organization: { title: '教育單位', value: '' },
                hasStartDate__DateEvent: { title: '開始時間', value: '' },
                hasEndDate__DateEvent: { title: '結束時間', value: '' },
                hasPlace__Place: { title: '地點', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 3,
        title: '工作',
        titleEn: 'Work',
        classType: 'EmploymentEvent',
        sub: [
            {
                label_employmentevent__string: { title: '工作名稱', value: '' },
                employmentType__string: { title: '工作類型', value: '' },
                jobTitle__string: { title: '職稱', value: '' },
                hasPlace__Place: { title: '工作地點', value: '' },
                hasEmployedAt__Organization: { title: '工作機構', value: '' },
                hasAlumni__Person: { title: '引薦者', value: '' },
                hasStartDate__DateEvent: { title: '開始時間', value: '' },
                hasEndDate__DateEvent: { title: '結束時間', value: '' },
                column__string: { title: '欄目名稱', value: '' },
                hasGenre__string: { title: '專欄類型', value: '' },
                hasColumnist__Person: { title: '專欄合寫者', value: '' },
                penName__string: { title: '筆名', value: '' },
                period__string: { title: '時長', value: '' },
                activity__string: { title: '活動名字', value: '' },
                workingArea__string: { title: '工作內容', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 4,
        title: '出版著作',
        titleEn: 'Publication',
        classType: 'Publication',
        sub: [
            {
                label_publication__string: { title: '書籍名稱', value: '' },
                hasGenre__string: { title: '類型', value: '' },
                column__string: { title: '欄目名稱', value: '' },
                comment__string: { title: '備註', value: '' },
                hasAuthor__Person: { title: '作家', value: '', need: true },
                hasDescribedTarget__Person: {
                    title: '作品描述之對象',
                    value: ''
                },
                desc__string: { title: '描述', value: '' },
                hasInceptionDate__DateEvent: { title: '出版日期', value: '' },
                hasPlaceOfPublication__Place: { title: '出版地點', value: '' },
                hasPublisher__Organization: { title: '出版', value: '' },
                hasReserved__Organization: { title: '典藏單位', value: '' },
                hasEditor__Person: { title: '編輯者', value: '' },
                hasBookPrefacer__Person: { title: '序跋者', value: '' },
                hasBookIncription__Person: { title: '題詞者', value: '' },
                hasBookTitle__Person: { title: '題籤者', value: '' },
                hasContributor__Person: { title: '貢獻者', value: '' },
                hasTranslator__Person: { title: '翻譯者', value: '' },
                issue__string: { title: '期號', value: '' },
                penName__string: { title: '筆名', value: '' },
                period__string: { title: '時長', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 5,
        title: '單篇文章',
        titleEn: 'Article',
        classType: 'Article',
        sub: [
            {
                label_article__string: { title: '文章名稱', value: '' },
                hasAuthor__Person: { title: '作家', value: '', need: true },
                hasDescribedTarget__Person: {
                    title: '作品描述之對象',
                    value: ''
                },
                hasGenre__string: { title: '專欄類型', value: '' },
                hasInceptionDate__DateEvent: { title: '發表日期', value: '' },
                hasPublishedIn__Publication: { title: '刊登於', value: '' },
                hasTranslator__Person: { title: '翻譯者', value: '' },
                owner__string: { title: '擁有者', value: '' },
                penName__string: { title: '使用的筆名', value: '' },
                publishYear__string: { title: '發表年', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 6,
        title: '其他作品',
        titleEn: 'Pieces',
        classType: 'OtherWork',
        sub: [
            {
                label_otherWork__string: { title: '作品名稱', value: '' },
                hasRelatedPerson__Person: {
                    title: '相關人物',
                    value: '',
                    need: true
                },
                hasRelatedOrganization__Organization: {
                    title: '相關組織',
                    value: ''
                },
                hasStartDate__DateEvent: { title: '日期', value: '' },
                otherWorkType__string: { title: '作品類型', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 7,
        title: '相關組織',
        titleEn: 'RelatedOrganizations',
        classType: 'organizationevent',
        sub: [
            {
                label_organizationevent__string: {
                    title: '組織名稱',
                    value: ''
                },
                hasFounded__Organization: { title: '創立組織', value: '' },
                hasLocationOfFormation__Place: { title: '成立地點', value: '' },
                hasParticipant__Organization: { title: '參與組織', value: '' },
                hasPlace__Place: { title: '組織地點', value: '' },
                organizationType__string: { title: '組織類型', value: '' },
                activity__string: { title: '活動名稱', value: '' },
                hasStartDate__DateEvent: { title: '開始時間', value: '' },
                hasEndDate__DateEvent: { title: '結束時間', value: '' },
                jobTitle__string: { title: '職稱', value: '' },
                period__string: { title: '時長', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 8,
        title: '人際關係',
        titleEn: 'Connections',
        classType: 'relationevent',
        sub: [
            {
                hasAdvisor__Person: { title: '顧問', value: '' },
                hasBiaoSyong__Person: { title: '表兄', value: '' },
                hasBrother__Person: { title: '兄弟', value: '' },
                hasBrotherInLaw__Person: { title: '妹夫', value: '' },
                hasClassmate__Person: { title: '同學', value: '' },
                hasColleague__Person: { title: '同事', value: '' },
                hasCousin__Person: { title: '堂兄弟', value: '' },
                hasDaughter__Person: { title: '女兒', value: '' },
                hasDescendants__Person: { title: '後代', value: '' },
                hasFather__Person: { title: '父親', value: '' },
                hasFatherInLaw__Person: { title: '岳父', value: '' },
                hasFirstDaughter__Person: { title: '長女', value: '' },
                hasFirstSon__Person: { title: '長子', value: '' },
                hasFriend__Person: { title: '友人', value: '' },
                hasFriendship__Person: { title: '朋友關係', value: '' },
                hasGrandFather__Person: { title: '祖父', value: '' },
                hasGrandMother__Person: { title: '祖母', value: '' },
                hasGrandSon__Person: { title: '孫', value: '' },
                hasGreatGrandSon__Person: { title: '曾子孫', value: '' },
                hasHired__Person: { title: '工作關係', value: '' },
                hasHusband__Person: { title: '丈夫', value: '' },
                hasIntroducer__Person: { title: '介紹人', value: '' },
                hasKinship__Person: { title: '親屬關係', value: '' },
                hasKnown__Person: { title: '認識', value: '' },
                hasMother__Person: { title: '母親', value: '' },
                hasMotherGrandSon__Person: { title: '外孫', value: '' },
                hasNephew__Person: { title: '姪子', value: '' },
                hasOlderSister__Person: { title: '姐姐', value: '' },
                hasParticipatePolitics__Person: {
                    title: '參與政治',
                    value: ''
                },
                hasProceededWith__Person: { title: '同遊', value: '' },
                hasSon__Person: { title: '兒子', value: '' },
                hasSonInLaw__Person: { title: '女婿', value: '' },
                hasSpouse__Person: { title: '夫妻', value: '' },
                hasStaffMemberWas__Person: { title: '有幕僚', value: '' },
                hasStudent__Person: { title: '學生', value: '' },
                hasSubordinateWas__Person: { title: '有下屬', value: '' },
                hasSupportedPolitics__Person: {
                    title: '贊助政治活動',
                    value: ''
                },
                hasTeacher__Person: { title: '老師', value: '' },
                hasTeacherStudent__Person: { title: '師生關係', value: '' },
                hasWife__Person: { title: '妻子', value: '' },
                hasWorkFor__Person: { title: '為…工作', value: '' },
                hasYaji__Person: { title: '雅集', value: '' },
                hasYoungerBrother__Person: { title: '弟弟', value: '' },
                hasYoungerSister__Person: { title: '妹妹', value: '' },
                relationRemarks__string: { title: '關係備註', value: '' }
            }
        ]
    },
    {
        id: 9,
        title: '相關事件',
        titleEn: 'RelativeEvent',
        classType: 'Event',
        sub: [
            {
                label_event__string: { title: '事件名稱', value: '' },
                hasEventPlace__Place: { title: '地點', value: '' },
                hasRelatedPerson__Person: { title: '相關人物', value: '' },
                hasRelatedOrganization__Organization: {
                    title: '相關組織',
                    value: ''
                },
                hasStartDate__DateEvent: { title: '開始時間', value: '' },
                hasEndDate__DateEvent: { title: '結束時間', value: '' },
                period__string: { title: '時長', value: '' },
                work__string: { title: '作品', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 10,
        title: '獎項',
        titleEn: 'Honor',
        classType: 'awardevent',
        sub: [
            {
                awardTitle__string: { title: '獲獎名稱', value: '' },
                hasAwardedForWork__Publication: {
                    title: '獲獎作品',
                    value: ''
                },
                hasPlace__Place: { title: '頒發地點', value: '' },
                wasConferredBy__Organization: { title: '頒發機構', value: '' },
                hasStartDate__DateEvent: { title: '日期', value: '' },
                wasConferredBy__Person: { title: '頒發者', value: '' },
                source__string: { title: '原始資料', value: '' }
            }
        ]
    },
    {
        id: 11,
        title: 'reviewSection',
        titleEn: 'ReviewSection',
        classType: 'ReviewEvent',
        sub: [
            {
                reviewData__string: {
                    title: 'ReviewType',
                    value: ''
                },
                reviewType__string: { title: 'Reviewdata', value: '' }
            }
        ]
    }
];

export default defaultEmptyData;
