import React from 'react';
import AccordionDetails from '@mui/material/AccordionDetails';
import TextField from '@mui/material/TextField';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import Grid from '@mui/system/Unstable_Grid';
import ChipInput from './ChipInput';
import CoordinatesModal from './CoordinatesModal';
import CustomDateInput from './CustomDateInput';
import { getGoogleCoord } from './utils';
import commentDisabledIcon from '../../../../images/chatgpt/comment-disabled.svg';
import commentIcon from '../../../../images/chatgpt/comment.svg';
import { commentsMapping } from '../../utils';
import { dateType, multiType } from '../../utils/inputType';

const SingleBlock = ({
    classType,
    initialData,
    changeFct,
    openCoordsModal,
    allCoordinates,
    toggleCoordsModal,
    handleChangeCoords,
    savingCoords,
    reviewStatus,
    comments
}) =>
    Object.values(initialData).map((detail, i) =>
        Object.entries(detail).map(([key, d], k) => (
            <AccordionDetails
                // eslint-disable-next-line react/no-array-index-key
                key={k}
                className="dataDetail"
                sx={{
                    '&.dataDetail': {
                        padding: '0'
                    }
                }}
            >
                <Grid
                    container
                    className="dataDetail_container"
                    sx={{
                        alignItems: 'center',
                        justifyContent: 'center'
                    }}
                >
                    {reviewStatus === 'reject' && (
                        <Grid xs={1}>
                            <Tooltip title={commentsMapping(d.id, comments)}>
                                <img
                                    src={commentsMapping(d.id, comments) ? commentIcon : commentDisabledIcon}
                                    alt="comment-icon"
                                />
                            </Tooltip>
                        </Grid>
                    )}
                    <Grid xs={3}>
                        <Typography variant="body3" sx={{ lineHeight: '20.27px', fontWeight: '600' }}>
                            {initialData.length > 1 ? `${d.title}${i + 1}` : d.title}
                        </Typography>
                    </Grid>
                    <Grid xs={8}>
                        {!dateType.includes(key) &&
                            !multiType.includes(key) &&
                            !key.endsWith('Place') &&
                            !key.endsWith('Organization') && (
                                <TextField
                                    fullWidth
                                    variant="standard"
                                    value={d.value}
                                    onChange={e => changeFct(i, key, e.target.value)}
                                    InputProps={{
                                        disableUnderline: true,
                                        sx: {
                                            lineHeight: '20.27px',
                                            borderRadius: '8px',
                                            border: '1px solid #EEEEEE',
                                            padding: '8px 12px',
                                            fontSize: '14px',
                                            '& .MuiInput-input': {
                                                padding: '0',
                                                margin: '0',
                                                fontFamily: '"Source Han Serif TC", sans-serif'
                                            }
                                        }
                                    }}
                                />
                            )}
                        {multiType.includes(key) && <ChipInput onchangeFct={changeFct} i={i} k={key} data={d.value} />}
                        {dateType.includes(key) && (
                            <CustomDateInput onchangeFct={changeFct} i={i} k={key} data={d.value} />
                        )}
                        {(key.endsWith('Place') || key.endsWith('Organization')) && (
                            <CoordinatesModal
                                open={openCoordsModal[`${classType}-${i}-${k}-${key}`] || false}
                                setOpen={isOpen => toggleCoordsModal(`${classType}-${i}-${k}-${key}`, isOpen)}
                                locations={d.value ? d.value.split('\n').filter(item => item !== '') : []}
                                title={d.title}
                                coordinateOptions={allCoordinates}
                                onClick={data => handleChangeCoords(data, i, key)}
                                getCoordinates={getGoogleCoord}
                                isLoading={savingCoords}
                            />
                        )}
                    </Grid>
                </Grid>
            </AccordionDetails>
        ))
    );

export default SingleBlock;
